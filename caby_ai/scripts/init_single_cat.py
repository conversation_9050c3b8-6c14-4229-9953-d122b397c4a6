#!/usr/bin/env python3
"""
初始化单只猫咪的特征向量
"""

import requests
import json
import base64
import argparse
import os
from PIL import Image
import io

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    try:
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            
            # 可选：调整图片大小
            image = Image.open(io.BytesIO(image_data))
            
            # 如果图片太大，调整大小
            max_size = (1024, 1024)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                buffer = io.BytesIO()
                format = image.format if image.format else 'JPEG'
                image.save(buffer, format=format)
                image_data = buffer.getvalue()
            
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return base64_string
            
    except Exception as e:
        print(f"❌ 处理图片失败: {e}")
        return None

def init_single_cat(api_key, user_id, cat_id, name, image_path):
    """初始化单只猫咪的特征"""
    base_url = "http://localhost:8765"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"🐱 初始化猫咪: {name}")
    print("=" * 40)
    print(f"用户ID: {user_id}")
    print(f"猫咪ID: {cat_id}")
    print(f"图片路径: {image_path}")
    
    # 检查文件
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return False
    
    # 转换图片
    print("📷 处理图片...")
    image_base64 = image_to_base64(image_path)
    if not image_base64:
        return False
    
    print(f"✅ 图片转换成功 (大小: {len(image_base64)} 字符)")
    
    # 发送请求
    print("📤 发送初始化请求...")
    
    request_data = {
        "user_id": user_id,
        "cats": [{
            "cat_id": cat_id,
            "name": name,
            "image_base64": image_base64
        }]
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/init-cats",
                               headers=headers, 
                               json=request_data,
                               timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                cat_result = result.get('results', [{}])[0]
                if cat_result.get('success'):
                    print(f"✅ {name} 初始化成功!")
                    return True
                else:
                    print(f"❌ {name} 初始化失败: {cat_result.get('error')}")
                    return False
            else:
                print(f"❌ 初始化失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='初始化单只猫咪的特征向量')
    parser.add_argument('--api-key', default='03U66tGbSQtHGrh9IyBDjRYaSeQukFga',
                       help='API密钥')
    parser.add_argument('--user-id', default='test_user_001',
                       help='用户ID')
    parser.add_argument('--cat-id', required=True,
                       help='猫咪ID')
    parser.add_argument('--name', required=True,
                       help='猫咪名称')
    parser.add_argument('--image', required=True,
                       help='猫咪图片路径')
    
    args = parser.parse_args()
    
    success = init_single_cat(args.api_key, args.user_id, args.cat_id, args.name, args.image)
    
    if success:
        print("\n🎉 初始化完成!")
        print(f"\n📋 验证命令:")
        print(f"   python scripts/test.py test_similarity --api-key {args.api_key}")
        return 0
    else:
        print("\n💥 初始化失败!")
        return 1

if __name__ == "__main__":
    exit(main())
