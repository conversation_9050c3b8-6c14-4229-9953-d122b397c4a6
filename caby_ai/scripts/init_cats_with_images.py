#!/usr/bin/env python3
"""
使用真实图片初始化猫咪特征向量
"""

import requests
import json
import base64
import argparse
import os
from PIL import Image
import io

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    try:
        with open(image_path, 'rb') as image_file:
            # 读取图片数据
            image_data = image_file.read()
            
            # 可选：调整图片大小以优化处理
            image = Image.open(io.BytesIO(image_data))
            
            # 如果图片太大，调整大小
            max_size = (1024, 1024)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                # 重新编码
                buffer = io.BytesIO()
                # 保持原始格式，如果是JPEG就用JPEG，PNG就用PNG
                format = image.format if image.format else 'JPEG'
                image.save(buffer, format=format)
                image_data = buffer.getvalue()
            
            # 转换为base64
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return base64_string
            
    except Exception as e:
        print(f"❌ 处理图片 {image_path} 失败: {e}")
        return None

def init_cats_with_images(api_key, user_id, cat_configs):
    """使用真实图片初始化猫咪特征"""
    base_url = "http://localhost:8765"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🐱 使用真实图片初始化猫咪特征向量")
    print("=" * 60)
    print(f"用户ID: {user_id}")
    print(f"猫咪数量: {len(cat_configs)}")
    print()
    
    # 准备猫咪数据
    cats_data = []
    
    for i, config in enumerate(cat_configs, 1):
        cat_id = config['cat_id']
        name = config['name']
        image_path = config['image_path']
        
        print(f"{i}. 处理 {name} (ID: {cat_id})")
        print(f"   图片路径: {image_path}")
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            print(f"   ❌ 图片文件不存在: {image_path}")
            return False
        
        # 转换图片为base64
        image_base64 = image_to_base64(image_path)
        if not image_base64:
            print(f"   ❌ 图片转换失败")
            return False
        
        print(f"   ✅ 图片转换成功 (大小: {len(image_base64)} 字符)")
        
        cats_data.append({
            "cat_id": cat_id,
            "name": name,
            "image_base64": image_base64
        })
    
    # 发送初始化请求
    print(f"\n📤 发送初始化请求...")
    
    request_data = {
        "user_id": user_id,
        "cats": cats_data
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/init-cats",
                               headers=headers, 
                               json=request_data,
                               timeout=120)  # 增加超时时间，因为处理真实图片可能需要更长时间
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print("✅ 猫咪特征初始化成功!")
                print("\n📋 初始化结果:")
                
                for cat_result in result.get('results', []):
                    status = "✅" if cat_result['success'] else "❌"
                    name = cat_result['name']
                    cat_id = cat_result['cat_id']
                    print(f"   {status} {name} (ID: {cat_id})")
                    
                    if not cat_result['success'] and 'error' in cat_result:
                        print(f"      错误: {cat_result['error']}")
                
                return True
            else:
                print(f"❌ 初始化失败: {result.get('error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='使用真实图片初始化猫咪特征向量')
    parser.add_argument('--api-key', default='03U66tGbSQtHGrh9IyBDjRYaSeQukFga',
                       help='API密钥')
    parser.add_argument('--user-id', default='test_user_001',
                       help='用户ID')
    parser.add_argument('--config', required=True,
                       help='猫咪配置JSON文件路径')
    
    args = parser.parse_args()
    
    # 读取配置文件
    try:
        with open(args.config, 'r', encoding='utf-8') as f:
            cat_configs = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return 1
    
    # 验证配置格式
    required_fields = ['cat_id', 'name', 'image_path']
    for i, config in enumerate(cat_configs):
        for field in required_fields:
            if field not in config:
                print(f"❌ 配置项 {i+1} 缺少必需字段: {field}")
                return 1
    
    # 执行初始化
    success = init_cats_with_images(args.api_key, args.user_id, cat_configs)
    
    if success:
        print("\n🎉 初始化完成!")
        print("\n📋 下一步:")
        print(f"   验证: python scripts/test.py test_similarity --api-key {args.api_key}")
        return 0
    else:
        print("\n💥 初始化失败!")
        return 1

if __name__ == "__main__":
    exit(main())
