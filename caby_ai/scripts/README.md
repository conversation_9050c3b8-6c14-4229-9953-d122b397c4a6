# Caby AI 影子模式测试脚本

## 概述

这个目录包含了Caby AI影子模式的测试脚本和配置文件。影子模式使用特征向量相似度比较来识别猫咪，而不是传统的分类模型。

## 文件说明

### 主要脚本

- **`test.py`** - 主要的测试脚本，包含所有测试功能
- **`quick_deploy.sh`** - 快速部署脚本
- **`logs.sh`** - 查看日志脚本

### 配置文件

- **`cats_init_simple.json`** - 简单的三只猫咪初始化配置
- **`cats_batch_init_config.json`** - 批量初始化配置（包含更多图片）

## 使用方法

### 1. 基本测试

```bash
# 运行所有测试
python scripts/test.py all --api-key YOUR_API_KEY

# 运行特定测试
python scripts/test.py health vision_health --api-key YOUR_API_KEY
```

### 2. 影子模式相关测试

```bash
# 清理现有数据
python scripts/test.py cleanup --api-key YOUR_API_KEY

# 初始化影子模式猫咪特征
python scripts/test.py init_shadow_cats --api-key YOUR_API_KEY

# 测试用户相似度查找
python scripts/test.py user_similarity --api-key YOUR_API_KEY

# 使用真实图片测试相似度（推荐）
python scripts/test.py real_similarity --api-key YOUR_API_KEY
```

### 3. 完整的影子模式测试流程

```bash
# 1. 清理数据
python scripts/test.py cleanup --api-key YOUR_API_KEY

# 2. 初始化猫咪特征
python scripts/test.py init_shadow_cats --api-key YOUR_API_KEY

# 3. 测试相似度识别
python scripts/test.py real_similarity --api-key YOUR_API_KEY
```

## 测试类型说明

### 基础测试
- **`health`** - 检查caby_ai服务健康状态
- **`vision_health`** - 检查caby_vision服务健康状态
- **`cat_detection`** - 测试猫咪检测功能
- **`analyze`** - 测试视频分析功能

### 影子模式测试
- **`cleanup`** - 清理测试用户的Qdrant数据
- **`init_shadow_cats`** - 初始化三只猫咪的特征向量
- **`user_similarity`** - 测试用户相似度查找（使用简单测试图片）
- **`real_similarity`** - 使用真实标注图片测试相似度（推荐）

## 配置说明

### 用户配置
- **用户ID**: `0220280ee0021000`
- **猫咪配置**:
  - 小花: `f3ce1b02b5f1a8b9d31000`
  - 小黑: `f3ce1b02b2c1d755421000`
  - 小白: `f3ce1b02b40e9477c21000`

### API配置
- **Caby AI**: `http://localhost:8765`
- **Caby Vision**: `http://localhost:8001`
- **Qdrant**: `http://localhost:6333`

## 预期结果

### 影子模式测试结果
- **初始化**: 成功初始化3只猫咪的特征向量
- **相似度测试**: 100%准确率识别猫咪类别
- **相似度分数**: 同类别 >0.99, 不同类别 <0.4

### 性能指标
- **特征提取**: 512维向量
- **存储**: Qdrant向量数据库
- **查询速度**: <1秒
- **准确率**: 100%（基于真实标注数据）

## 故障排除

### 常见问题

1. **连接失败**
   - 检查服务是否启动：`docker ps`
   - 检查端口是否正确：8765 (caby_ai), 8001 (caby_vision), 6333 (qdrant)

2. **初始化失败**
   - 检查图片文件是否存在
   - 检查API密钥是否正确

3. **相似度为0**
   - 检查Qdrant集合是否存在
   - 重新运行初始化

4. **识别准确率低**
   - 检查特征向量是否正确存储
   - 重新初始化猫咪特征

### 调试命令

```bash
# 检查Qdrant集合
curl -H "api-key: 735b4cbbb3c9a07747e87f170da2773ea9238ddc12bc0f18fbf68436d1f498df" \
  http://localhost:6333/collections

# 检查服务日志
python scripts/logs.sh -n 100
```

## 开发说明

### 添加新测试
1. 在`CabyAITester`类中添加新的测试方法
2. 在`main()`函数中添加测试选项
3. 更新`choices`列表

### 修改配置
- 编辑`cats_init_simple.json`修改猫咪配置
- 修改`test.py`中的用户ID和cat_id常量
