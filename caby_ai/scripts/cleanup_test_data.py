#!/usr/bin/env python3
"""
清理测试数据脚本
"""

import requests
import json

def cleanup_test_data():
    """清理测试数据"""
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    user_id = "test_user_001"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🧹 清理测试数据")
    print("=" * 60)
    
    # 方法1：通过Qdrant API直接删除集合
    print("\n1️⃣ 删除用户特征集合...")
    
    # 构造用户集合名称
    collection_name = f"cat_features_user_{user_id}"
    qdrant_url = "http://localhost:6333"
    qdrant_headers = {
        "api-key": "735b4cbbb3c9a07747e87f170da2773ea9238ddc12bc0f18fbf68436d1f498df",
        "Content-Type": "application/json"
    }
    
    try:
        # 删除集合
        response = requests.delete(f"{qdrant_url}/collections/{collection_name}", 
                                 headers=qdrant_headers)
        if response.status_code == 200:
            print(f"✅ 成功删除集合: {collection_name}")
        elif response.status_code == 404:
            print(f"ℹ️  集合不存在: {collection_name}")
        else:
            print(f"⚠️  删除集合失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ 删除集合异常: {e}")
    
    # 检查是否还有其他相关集合
    print("\n2️⃣ 检查现有集合...")
    try:
        response = requests.get(f"{qdrant_url}/collections", headers=qdrant_headers)
        if response.status_code == 200:
            collections = response.json()
            print("现有集合:")
            for collection in collections.get('result', {}).get('collections', []):
                collection_name = collection.get('name', 'unknown')
                print(f"   - {collection_name}")
                
                # 如果是测试相关的集合，询问是否删除
                if 'test_user' in collection_name or 'test_collection' in collection_name:
                    print(f"   ⚠️  发现测试集合: {collection_name}")
                    try:
                        del_response = requests.delete(f"{qdrant_url}/collections/{collection_name}", 
                                                     headers=qdrant_headers)
                        if del_response.status_code == 200:
                            print(f"   ✅ 已删除: {collection_name}")
                        else:
                            print(f"   ❌ 删除失败: {del_response.status_code}")
                    except Exception as e:
                        print(f"   ❌ 删除异常: {e}")
        else:
            print(f"❌ 获取集合列表失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 获取集合列表异常: {e}")
    
    print("\n✅ 数据清理完成!")
    print("\n📋 下一步:")
    print("   1. 运行: python scripts/test.py init_cats --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga")
    print("   2. 验证: python scripts/test.py test_similarity --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga")

if __name__ == "__main__":
    cleanup_test_data()
