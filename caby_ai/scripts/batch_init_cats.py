#!/usr/bin/env python3
"""
批量初始化猫咪特征向量 - 支持每只猫多张图片
"""

import requests
import json
import base64
import argparse
import os
from PIL import Image
import io
import time

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    try:
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            
            # 可选：调整图片大小以优化处理
            image = Image.open(io.BytesIO(image_data))
            
            # 如果图片太大，调整大小
            max_size = (1024, 1024)
            if image.size[0] > max_size[0] or image.size[1] > max_size[1]:
                image.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                buffer = io.BytesIO()
                format = image.format if image.format else 'JPEG'
                image.save(buffer, format=format)
                image_data = buffer.getvalue()
            
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return base64_string
            
    except Exception as e:
        print(f"❌ 处理图片 {image_path} 失败: {e}")
        return None

def batch_init_cats(api_key, config_file):
    """批量初始化猫咪特征"""
    base_url = "http://localhost:8765"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 加载配置文件
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False
    
    user_id = config.get('user_id')
    cats = config.get('cats', [])
    
    print("🐱 批量初始化猫咪特征向量")
    print("=" * 60)
    print(f"用户ID: {user_id}")
    print(f"猫咪数量: {len(cats)}")
    
    total_images = sum(len(cat['images']) for cat in cats)
    print(f"总图片数量: {total_images}")
    print()
    
    # 为每只猫的每张图片创建初始化请求
    success_count = 0
    total_count = 0
    
    for cat_idx, cat in enumerate(cats, 1):
        cat_id = cat['cat_id']
        cat_name = cat['name']
        images = cat['images']
        
        print(f"{cat_idx}. 处理 {cat_name} (ID: {cat_id}) - {len(images)} 张图片")
        
        for img_idx, image_path in enumerate(images, 1):
            total_count += 1
            
            print(f"   {img_idx:3d}/{len(images)} 处理图片: {os.path.basename(image_path)}")
            
            # 检查文件是否存在
            if not os.path.exists(image_path):
                print(f"      ❌ 图片文件不存在")
                continue
            
            # 转换图片为base64
            image_base64 = image_to_base64(image_path)
            if not image_base64:
                print(f"      ❌ 图片转换失败")
                continue
            
            # 创建唯一的cat_id（添加图片序号）
            unique_cat_id = f"{cat_id}_{img_idx:03d}"
            unique_cat_name = f"{cat_name}_{img_idx:03d}"
            
            # 发送初始化请求
            request_data = {
                "user_id": user_id,
                "cats": [{
                    "cat_id": unique_cat_id,
                    "name": unique_cat_name,
                    "image_base64": image_base64
                }]
            }
            
            try:
                response = requests.post(f"{base_url}/api/v1/admin/init-cats",
                                       headers=headers, 
                                       json=request_data,
                                       timeout=60)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    if result.get('success'):
                        cat_result = result.get('results', [{}])[0]
                        if cat_result.get('success'):
                            print(f"      ✅ 初始化成功")
                            success_count += 1
                        else:
                            print(f"      ❌ 初始化失败: {cat_result.get('error')}")
                    else:
                        print(f"      ❌ 请求失败: {result.get('error')}")
                else:
                    print(f"      ❌ HTTP错误: {response.status_code}")
                    
            except Exception as e:
                print(f"      ❌ 请求异常: {e}")
            
            # 添加小延迟避免过载
            time.sleep(0.1)

            # 每10张图片显示进度
            if total_count % 10 == 0:
                print(f"   📊 总进度: {total_count}/{total_images} ({total_count/total_images*100:.1f}%)")
        
        print()
    
    print(f"📊 批量初始化完成!")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有图片初始化成功!")
        return True
    else:
        print("⚠️  部分图片初始化失败")
        return False

def main():
    parser = argparse.ArgumentParser(description='批量初始化猫咪特征向量')
    parser.add_argument('--api-key', default='03U66tGbSQtHGrh9IyBDjRYaSeQukFga',
                       help='API密钥')
    parser.add_argument('--config', required=True,
                       help='批量配置文件路径')
    
    args = parser.parse_args()
    
    success = batch_init_cats(args.api_key, args.config)
    
    if success:
        print("\n🎉 批量初始化完成!")
        print(f"\n📋 验证命令:")
        print(f"   python scripts/test.py test_similarity --api-key {args.api_key}")
        return 0
    else:
        print("\n💥 批量初始化失败!")
        return 1

if __name__ == "__main__":
    exit(main())
