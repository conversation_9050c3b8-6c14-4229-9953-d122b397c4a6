#!/usr/bin/env python3
"""
测试批量初始化猫咪特征功能
使用配置文件中的100只猫进行测试
"""

import json
import os
import base64
import requests
import time
from typing import List, Dict, Any

class BatchInitTester:
    def __init__(self):
        self.base_url = "http://localhost:8080"
        self.api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
    def log_info(self, message: str):
        print(f"ℹ️  {message}")
        
    def log_success(self, message: str):
        print(f"✅ {message}")
        
    def log_error(self, message: str):
        print(f"❌ {message}")
        
    def log_warning(self, message: str):
        print(f"⚠️  {message}")

    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.log_error(f"加载配置文件失败: {e}")
            return {}

    def prepare_batch_data(self, config_data: Dict[str, Any], user_id: str) -> Dict[str, Any]:
        """准备批量初始化数据"""
        cats_config = config_data.get('cats', [])
        self.log_info(f"从配置文件加载了 {len(cats_config)} 只猫的配置")

        cats_data = []
        total_images = 0

        for i, cat_config in enumerate(cats_config, 1):
            cat_id = cat_config['cat_id']
            name = cat_config['name']
            image_paths = cat_config['images']

            self.log_info(f"{i}. 处理 {name} (ID: {cat_id}) - {len(image_paths)} 张图片")

            # 处理该猫咪的所有图片
            cat_images = []
            valid_images = 0

            for j, image_path in enumerate(image_paths):
                # 检查文件是否存在
                if not os.path.exists(image_path):
                    self.log_warning(f"   图片 {j+1}/{len(image_paths)} 不存在: {os.path.basename(image_path)}")
                    continue

                # 转换图片为base64
                try:
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                        image_base64 = base64.b64encode(image_data).decode()
                    
                    cat_images.append(image_base64)
                    valid_images += 1
                    total_images += 1
                    
                    if j % 20 == 0:  # 每20张图片显示一次进度
                        self.log_info(f"   已处理 {j+1}/{len(image_paths)} 张图片...")
                        
                except Exception as e:
                    self.log_warning(f"   图片 {j+1} 转换失败: {e}")
                    continue

            if valid_images == 0:
                self.log_error(f"   {name} 没有有效的图片，跳过")
                continue

            self.log_success(f"   {name} 成功处理 {valid_images}/{len(image_paths)} 张图片")

            cats_data.append({
                "cat_id": cat_id,
                "name": name,
                "images": cat_images
            })

        self.log_info(f"📊 总计: {len(cats_data)} 只猫, {total_images} 张图片")

        return {
            "user_id": user_id,
            "cats": cats_data
        }

    def test_batch_init(self, user_id: str = "022b605dc3421000") -> bool:
        """测试批量初始化功能"""
        self.log_info("🚀 开始批量初始化测试")
        print("=" * 60)

        # 加载配置文件
        config_path = "/home/<USER>/animsi/aby/server/caby_ai/scripts/cats_batch_init_config.json"
        config_data = self.load_config(config_path)
        
        if not config_data:
            return False

        # 准备批量数据
        request_data = self.prepare_batch_data(config_data, user_id)
        
        if not request_data.get('cats'):
            self.log_error("没有有效的猫咪数据")
            return False

        # 发送批量初始化请求
        self.log_info("📤 发送批量初始化请求...")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/v1/admin/batch-init-cats",
                headers=self.headers, 
                json=request_data,
                timeout=600  # 10分钟超时
            )
            end_time = time.time()
            
            response.raise_for_status()
            result = response.json()

            self.log_info(f"⏱️  请求耗时: {end_time - start_time:.2f} 秒")

            if result.get('success'):
                self.log_success("猫咪特征批量初始化成功!")

                # 显示结果统计
                self.log_info("📋 初始化结果统计:")
                results = result.get('results', [])
                success_count = sum(1 for r in results if r.get('success'))
                total_count = len(results)
                
                self.log_success(f"   成功: {success_count}/{total_count} 只猫")
                
                # 显示详细结果
                for cat_result in results:
                    if cat_result.get('success'):
                        processed_images = cat_result.get('processed_images', 0)
                        total_images = cat_result.get('total_images', 0)
                        self.log_success(f"   ✅ {cat_result['name']} (ID: {cat_result['cat_id']}) - {processed_images}/{total_images} 张图片")
                    else:
                        self.log_error(f"   ❌ {cat_result['name']} (ID: {cat_result['cat_id']}): {cat_result.get('error')}")

                # 显示总体统计
                total_processed_images = sum(r.get('processed_images', 0) for r in results if r.get('success'))
                total_expected_images = sum(r.get('total_images', 0) for r in results)
                self.log_info(f"📊 图片处理统计: {total_processed_images}/{total_expected_images} 张")

                return True
            else:
                self.log_error(f"猫咪特征批量初始化失败: {result.get('error')}")
                return False

        except requests.exceptions.Timeout:
            self.log_error("请求超时，批量初始化可能需要更长时间")
            return False
        except Exception as e:
            self.log_error(f"批量初始化请求失败: {e}")
            return False

def main():
    """主函数"""
    print("🐱 批量初始化猫咪特征测试")
    print("=" * 60)
    
    tester = BatchInitTester()
    
    # 测试批量初始化
    success = tester.test_batch_init()
    
    if success:
        print("\n🎉 批量初始化测试成功！")
        print("\n📋 后续步骤:")
        print("   1. 运行相似度测试验证结果")
        print("   2. 检查Qdrant中的特征向量")
        print("   3. 测试影子模式功能")
    else:
        print("\n❌ 批量初始化测试失败")
        print("\n🔧 故障排除:")
        print("   1. 检查caby_ai服务状态")
        print("   2. 验证配置文件路径")
        print("   3. 确认图片文件存在")

if __name__ == "__main__":
    main()
