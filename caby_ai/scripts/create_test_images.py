#!/usr/bin/env python3
"""
创建测试用的猫咪图片
"""

from PIL import Image, ImageDraw, ImageFont
import os
import json

def create_test_cat_image(name, color, size=(512, 512)):
    """创建一个测试用的猫咪图片"""
    # 创建图片
    image = Image.new('RGB', size, color=color)
    draw = ImageDraw.Draw(image)
    
    # 尝试使用系统字体，如果失败则使用默认字体
    try:
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 48)
    except:
        try:
            font = ImageFont.truetype("arial.ttf", 48)
        except:
            font = ImageFont.load_default()
    
    # 在图片上写猫咪名字
    text = f"Cat: {name}"
    
    # 计算文本位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size[0] - text_width) // 2
    y = (size[1] - text_height) // 2
    
    # 绘制文本（白色文字，黑色边框）
    draw.text((x-1, y-1), text, font=font, fill='black')
    draw.text((x+1, y-1), text, font=font, fill='black')
    draw.text((x-1, y+1), text, font=font, fill='black')
    draw.text((x+1, y+1), text, font=font, fill='black')
    draw.text((x, y), text, font=font, fill='white')
    
    # 画一些简单的猫咪特征（耳朵）
    ear_size = 60
    # 左耳
    draw.polygon([(x-100, y-80), (x-100+ear_size, y-80-ear_size), (x-100+ear_size, y-80)], fill='white')
    # 右耳  
    draw.polygon([(x+100, y-80), (x+100+ear_size, y-80), (x+100+ear_size, y-80-ear_size)], fill='white')
    
    return image

def main():
    """创建测试图片和配置文件"""
    # 创建图片目录
    image_dir = "test_cat_images"
    os.makedirs(image_dir, exist_ok=True)
    
    # 定义猫咪数据
    cats = [
        {
            "cat_id": "f3ce1b02b2c1d755421000",
            "name": "小黑",
            "color": (50, 50, 50),  # 深灰色代表黑猫
            "filename": "xiaohei.jpg"
        },
        {
            "cat_id": "f3ce1b02b40e9477c21000", 
            "name": "小白",
            "color": (240, 240, 240),  # 浅灰色代表白猫
            "filename": "xiaobai.jpg"
        },
        {
            "cat_id": "f3ce1b02b5f1a8b9d31000",
            "name": "小花", 
            "color": (200, 150, 100),  # 棕色代表花猫
            "filename": "xiaohua.jpg"
        }
    ]
    
    print("🎨 创建测试猫咪图片")
    print("=" * 40)
    
    # 创建图片
    config_data = []
    
    for cat in cats:
        print(f"创建 {cat['name']} 的图片...")
        
        # 创建图片
        image = create_test_cat_image(cat['name'], cat['color'])
        
        # 保存图片
        image_path = os.path.join(image_dir, cat['filename'])
        image.save(image_path, 'JPEG', quality=95)
        
        print(f"✅ 保存到: {image_path}")
        
        # 添加到配置
        config_data.append({
            "cat_id": cat['cat_id'],
            "name": cat['name'],
            "image_path": os.path.abspath(image_path)
        })
    
    # 创建配置文件
    config_file = "test_cats_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 配置文件已创建: {config_file}")
    
    print(f"\n🚀 使用方法:")
    print(f"python scripts/init_cats_with_images.py --config {config_file}")
    
    print(f"\n📁 创建的文件:")
    print(f"   配置文件: {os.path.abspath(config_file)}")
    for cat in cats:
        image_path = os.path.join(image_dir, cat['filename'])
        print(f"   {cat['name']}: {os.path.abspath(image_path)}")

if __name__ == "__main__":
    main()
