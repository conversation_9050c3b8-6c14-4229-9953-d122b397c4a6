#!/usr/bin/env python3
"""
使用真实猫咪图片测试特征提取
"""

import requests
import json
import base64
import os
import random

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    try:
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return base64_string
    except Exception as e:
        print(f"❌ 处理图片失败: {e}")
        return None

def test_real_feature_extraction():
    """使用真实猫咪图片测试特征提取"""
    vision_url = "http://localhost:8001"
    api_key = "bcfa6d713db19f5888cad77a84714b8563b3529f87be94a0ec53460e5e7d1602"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🔍 使用真实猫咪图片测试特征提取")
    print("=" * 60)
    
    # 从annotations.json中获取不同类别的图片
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    image_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    
    try:
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
    except Exception as e:
        print(f"❌ 加载标注文件失败: {e}")
        return False
    
    # 按类别分组
    categories = {'小花': [], '小黑': [], '小白': []}
    
    for image_name, data in annotations.items():
        category = data.get('category', '')
        if category in categories:
            image_path = os.path.join(image_dir, image_name)
            if os.path.exists(image_path):
                categories[category].append({
                    'name': image_name,
                    'path': image_path
                })
    
    # 每个类别选择2张图片进行测试
    test_images = []
    for category, images in categories.items():
        if len(images) >= 2:
            selected = random.sample(images, 2)
            for img in selected:
                test_images.append({
                    'category': category,
                    'name': img['name'],
                    'path': img['path']
                })
    
    print(f"选择了 {len(test_images)} 张测试图片")
    
    # 提取特征
    features_data = []
    
    for i, img_info in enumerate(test_images, 1):
        print(f"\n{i}. 处理 {img_info['category']} - {img_info['name']}")
        
        # 转换图片为base64
        image_base64 = image_to_base64(img_info['path'])
        if not image_base64:
            continue
        
        # 提取特征
        request_data = {
            "image": image_base64,
            "user_id": "test_user",
            "task": "extract_features"
        }
        
        try:
            response = requests.post(f"{vision_url}/featured/extract", 
                                   headers=headers, 
                                   json=request_data,
                                   timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('results'):
                    features = result['results'].get('features', [])
                    print(f"   ✅ 特征提取成功，维度: {len(features)}")
                    print(f"   前5个特征值: {features[:5]}")
                    print(f"   特征范围: [{min(features):.4f}, {max(features):.4f}]")
                    
                    features_data.append({
                        'category': img_info['category'],
                        'name': img_info['name'],
                        'features': features
                    })
                else:
                    print(f"   ❌ 特征提取失败: {result}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
    
    # 分析特征相似度
    print(f"\n📊 特征相似度分析")
    print("=" * 40)
    
    if len(features_data) >= 2:
        import math
        
        def cosine_similarity(a, b):
            dot_product = sum(x * y for x, y in zip(a, b))
            magnitude_a = math.sqrt(sum(x * x for x in a))
            magnitude_b = math.sqrt(sum(x * x for x in b))
            if magnitude_a == 0 or magnitude_b == 0:
                return 0
            return dot_product / (magnitude_a * magnitude_b)
        
        # 计算同类别和不同类别的相似度
        same_category_similarities = []
        diff_category_similarities = []
        
        for i in range(len(features_data)):
            for j in range(i + 1, len(features_data)):
                img1 = features_data[i]
                img2 = features_data[j]
                
                similarity = cosine_similarity(img1['features'], img2['features'])
                
                if img1['category'] == img2['category']:
                    same_category_similarities.append(similarity)
                    print(f"同类别 {img1['category']}: {similarity:.4f}")
                else:
                    diff_category_similarities.append(similarity)
                    print(f"不同类别 {img1['category']} vs {img2['category']}: {similarity:.4f}")
        
        # 统计分析
        if same_category_similarities:
            avg_same = sum(same_category_similarities) / len(same_category_similarities)
            print(f"\n同类别平均相似度: {avg_same:.4f}")
        
        if diff_category_similarities:
            avg_diff = sum(diff_category_similarities) / len(diff_category_similarities)
            print(f"不同类别平均相似度: {avg_diff:.4f}")
        
        # 判断特征质量
        if same_category_similarities and diff_category_similarities:
            if avg_same > avg_diff + 0.1:  # 同类别相似度应该明显高于不同类别
                print("✅ 特征提取质量良好，能够区分不同类别")
                return True
            else:
                print("⚠️  特征提取质量可能有问题，类别区分度不够")
                return False
        else:
            print("⚠️  测试数据不足，无法判断特征质量")
            return False
    else:
        print("❌ 特征数据不足，无法进行分析")
        return False

if __name__ == "__main__":
    success = test_real_feature_extraction()
    exit(0 if success else 1)
