# config/config.yaml
server:
  listen_addr: ":8765"
  max_concurrency: 2  # Maximum number of segments to process concurrently (controls CPU usage)

auth:
  # This token should match the one used by backend_server when calling caby_ai
  service_token: "${CABY_AI_SERVICE_TOKEN}"

qdrant:
  host: "${QDRANT_HOST}:${QDRANT_PORT}" # Qdrant service address with port
  scheme: "${QDRANT_SCHEME:-http}" # http or https
  api_key: "${QDRANT_API_KEY}" # API key for Qdrant authentication

vision:
  host: "${VISION_HOST}" # Docker service name or IP
  port: 8001
  api_key: "${VISION_API_KEY}" # Vision API密钥，通过环境变量设置
  timeout: 30 # Request timeout in seconds

shadow_mode:
  enabled: ${SHADOW_MODE_ENABLED}
  similarity_threshold: ${SHADOW_SIMILARITY_THRESHOLD}
  new_cat_threshold: ${SHADOW_NEW_CAT_THRESHOLD}
  top_k: ${SHADOW_TOP_K}

backend_server_url: "https://api.caby.care" # URL of the backend_server
