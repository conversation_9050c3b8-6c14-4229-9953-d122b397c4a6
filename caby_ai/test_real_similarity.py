#!/usr/bin/env python3
"""
使用真实标注图片测试相似度查找
"""

import requests
import json
import base64
import os
import random

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    try:
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return base64_string
    except Exception as e:
        print(f"❌ 处理图片失败: {e}")
        return None

def load_annotations():
    """加载标注数据"""
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    try:
        with open(annotations_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加载标注文件失败: {e}")
        return None

def get_test_images_by_category():
    """获取每个类别的测试图片"""
    annotations = load_annotations()
    if not annotations:
        return None
    
    image_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    
    # 按类别分组
    categories = {'小花': [], '小黑': [], '小白': []}
    
    for image_name, data in annotations.items():
        category = data.get('category', '')
        if category in categories:
            image_path = os.path.join(image_dir, image_name)
            if os.path.exists(image_path):
                categories[category].append({
                    'name': image_name,
                    'path': image_path
                })
    
    # 每个类别随机选择3张图片进行测试
    test_images = {}
    for category, images in categories.items():
        if len(images) >= 3:
            test_images[category] = random.sample(images, 3)
        else:
            test_images[category] = images
    
    return test_images

def test_similarity_with_real_image(user_id, image_path, image_name, expected_category):
    """使用真实图片测试相似度"""
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"\n🔍 测试图片: {image_name}")
    print(f"   预期类别: {expected_category}")
    
    # 转换图片为base64
    image_base64 = image_to_base64(image_path)
    if not image_base64:
        return False
    
    request_data = {
        "user_id": user_id,
        "image_base64": image_base64,
        "limit": 10
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/test-similarity",
                               headers=headers, 
                               json=request_data,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                results = result.get('results', [])
                print(f"   ✅ 找到 {len(results)} 个相似结果")
                
                if not results:
                    print("   ⚠️  没有找到相似结果")
                    return False
                
                # 分析结果
                category_scores = {'小花': [], '小黑': [], '小白': []}
                
                for item in results[:5]:  # 只看前5个结果
                    payload = item.get('payload', {})
                    original_id = payload.get('original_id', 'unknown')
                    similarity = item.get('score', 0)
                    
                    # 根据ID判断类别
                    if 'f3ce1b02b5f1a8b9d31000' in original_id:  # 小花
                        category_scores['小花'].append(similarity)
                    elif 'f3ce1b02b2c1d755421000' in original_id:  # 小黑
                        category_scores['小黑'].append(similarity)
                    elif 'f3ce1b02b40e9477c21000' in original_id:  # 小白
                        category_scores['小白'].append(similarity)
                    
                    print(f"      ID: {original_id[:30]}... 相似度: {similarity:.4f}")
                
                # 计算每个类别的平均相似度
                avg_scores = {}
                for cat, scores in category_scores.items():
                    if scores:
                        avg_scores[cat] = sum(scores) / len(scores)
                        print(f"   📊 {cat} 平均相似度: {avg_scores[cat]:.4f} ({len(scores)}个结果)")
                
                # 判断是否识别正确
                if avg_scores:
                    best_category = max(avg_scores.items(), key=lambda x: x[1])
                    print(f"   🎯 最佳匹配: {best_category[0]} (相似度: {best_category[1]:.4f})")
                    
                    if best_category[0] == expected_category:
                        print("   ✅ 识别正确!")
                        return True
                    else:
                        print(f"   ❌ 识别错误! 预期: {expected_category}, 实际: {best_category[0]}")
                        return False
                else:
                    print("   ⚠️  无法计算类别相似度")
                    return False
                
            else:
                print(f"   ❌ 查找失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def main():
    user_id = "0220280ee0021000"
    
    print("🐱 使用真实标注图片测试相似度")
    print("=" * 60)
    print(f"用户ID: {user_id}")
    
    # 获取测试图片
    test_images = get_test_images_by_category()
    if not test_images:
        print("❌ 无法获取测试图片")
        return 1
    
    total_tests = 0
    correct_tests = 0
    
    # 测试每个类别的图片
    for category, images in test_images.items():
        print(f"\n📂 测试类别: {category} ({len(images)} 张图片)")
        
        for image_info in images:
            total_tests += 1
            success = test_similarity_with_real_image(
                user_id, 
                image_info['path'], 
                image_info['name'], 
                category
            )
            if success:
                correct_tests += 1
    
    # 显示总结
    print(f"\n📊 测试总结")
    print("=" * 40)
    print(f"总测试数: {total_tests}")
    print(f"正确识别: {correct_tests}")
    print(f"准确率: {correct_tests/total_tests*100:.1f}%" if total_tests > 0 else "无测试数据")
    
    return 0 if correct_tests == total_tests else 1

if __name__ == "__main__":
    exit(main())
