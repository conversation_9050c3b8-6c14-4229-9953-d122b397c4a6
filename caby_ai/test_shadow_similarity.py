#!/usr/bin/env python3
"""
测试影子模式相似度搜索功能
"""

import requests
import json
import base64
from PIL import Image
import io

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (224, 224), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_shadow_similarity():
    """测试影子模式相似度搜索功能"""
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    user_id = "test_user_001"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🔍 测试影子模式相似度搜索")
    print("=" * 60)
    
    # 1. 测试影子模式配置
    print("\n1️⃣ 获取影子模式配置...")
    try:
        response = requests.get(f"{base_url}/api/v1/admin/shadow-config", headers=headers)
        if response.status_code == 200:
            config = response.json()
            print(f"✅ 配置获取成功:")
            print(f"   启用状态: {config['config']['enabled']}")
            print(f"   相似度阈值: {config['config']['similarity_threshold']}")
            print(f"   新猫阈值: {config['config']['new_cat_threshold']}")
            print(f"   Top K: {config['config']['top_k']}")
        else:
            print(f"❌ 配置获取失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 配置获取异常: {e}")
        return False
    
    # 2. 测试相似度搜索
    print("\n2️⃣ 测试相似度搜索...")
    test_image = create_test_image()
    
    payload = {
        "user_id": user_id,
        "image_base64": test_image,
        "limit": 5
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/test-similarity", 
                               headers=headers, 
                               json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 相似度搜索成功:")
            print(f"   找到 {len(result.get('results', []))} 个结果")
            
            for i, cat in enumerate(result.get('results', []), 1):
                cat_id = cat.get('payload', {}).get('original_id', cat.get('id', 'N/A'))
                cat_name = cat.get('payload', {}).get('cat_name', 'Unknown')
                similarity = cat.get('score', 0)
                print(f"   {i}. {cat_name} (ID: {cat_id}) - 相似度: {similarity:.4f}")
            
            return True
        else:
            print(f"❌ 相似度搜索失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 相似度搜索异常: {e}")
        return False

if __name__ == "__main__":
    success = test_shadow_similarity()
    if success:
        print("\n🎉 影子模式相似度搜索测试通过!")
    else:
        print("\n💥 影子模式相似度搜索测试失败!")
        exit(1)
