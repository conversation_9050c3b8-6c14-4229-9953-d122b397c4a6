#!/usr/bin/env python3
"""
测试影子模式功能
"""

import requests
import json
import base64
from PIL import Image
import io
import numpy as np

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (224, 224), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_shadow_mode():
    """测试影子模式功能"""
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    user_id = "test_user_001"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🧪 测试影子模式功能")
    print("=" * 60)
    
    # 1. 测试影子模式配置
    print("\n1️⃣ 获取影子模式配置...")
    try:
        response = requests.get(f"{base_url}/api/v1/admin/shadow-config", headers=headers)
        if response.status_code == 200:
            config = response.json()
            print(f"✅ 配置获取成功: {config}")
        else:
            print(f"❌ 配置获取失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 配置获取异常: {e}")
        return False
    
    # 2. 测试影子模式分析（通过主分析端点）
    print("\n2️⃣ 测试影子模式分析...")

    # 创建一个模拟的记录分析请求
    payload = {
        "video_id": "test_video_001",
        "device_id": "test_device_001",
        "user_id": user_id,
        "start_time": "2025-01-16T10:00:00Z",
        "end_time": "2025-01-16T10:01:00Z",
        "video_path": "http://example.com/test.m3u8"
    }

    try:
        response = requests.post(f"{base_url}/api/v1/analyze",
                               headers=headers,
                               json=payload)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 分析成功:")
            print(f"   分析ID: {result.get('analysis_id', 'N/A')}")
            print(f"   预测猫咪: {result.get('predicted_cat', 'N/A')}")
            print(f"   置信度: {result.get('confidence', 'N/A')}")
            # 检查影子模式相关字段
            if 'shadow_mode_result' in result:
                print(f"   影子模式结果: {result.get('shadow_mode_result', 'N/A')}")
                print(f"   影子相似度: {result.get('shadow_similarity', 'N/A')}")
                print(f"   影子匹配猫ID: {result.get('shadow_matched_cat_id', 'N/A')}")
            return True
        else:
            print(f"❌ 分析失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 分析异常: {e}")
        return False

if __name__ == "__main__":
    success = test_shadow_mode()
    if success:
        print("\n🎉 影子模式测试通过!")
    else:
        print("\n💥 影子模式测试失败!")
        exit(1)
