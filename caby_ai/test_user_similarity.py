#!/usr/bin/env python3
"""
测试指定用户的相似度查找
"""

import requests
import json
import base64
from PIL import Image
import io

def create_test_image():
    """创建一个测试图片"""
    img = Image.new('RGB', (224, 224), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_user_similarity(user_id="0220280ee0021000", limit=10):
    """测试用户相似度查找"""
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print(f"🔍 测试用户 {user_id} 的相似度查找")
    print("=" * 60)
    
    # 创建测试图片
    test_image = create_test_image()
    
    request_data = {
        "user_id": user_id,
        "image_base64": test_image,
        "limit": limit
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/test-similarity",
                               headers=headers, 
                               json=request_data,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                results = result.get('results', [])
                print(f"✅ 找到 {len(results)} 个相似结果")
                
                # 按猫咪分组显示
                cat_groups = {}
                for item in results:
                    payload = item.get('payload', {})
                    original_id = payload.get('original_id', 'unknown')
                    cat_name = payload.get('cat_name', 'Unknown')
                    similarity = item.get('score', 0)
                    
                    # 提取基础cat_id（去掉序号）
                    base_cat_id = original_id.split('_')[0] if '_' in original_id else original_id
                    
                    if base_cat_id not in cat_groups:
                        cat_groups[base_cat_id] = {
                            'name': cat_name.split('_')[0] if '_' in cat_name else cat_name,
                            'items': []
                        }
                    
                    cat_groups[base_cat_id]['items'].append({
                        'id': original_id,
                        'name': cat_name,
                        'similarity': similarity
                    })
                
                # 显示结果
                for base_id, group in cat_groups.items():
                    print(f"\n🐱 {group['name']} (基础ID: {base_id})")
                    print(f"   找到 {len(group['items'])} 个特征向量")
                    
                    # 显示前5个最相似的
                    sorted_items = sorted(group['items'], key=lambda x: x['similarity'], reverse=True)
                    for i, item in enumerate(sorted_items[:5], 1):
                        print(f"   {i}. {item['name']} - 相似度: {item['similarity']:.4f}")
                    
                    if len(sorted_items) > 5:
                        print(f"   ... 还有 {len(sorted_items) - 5} 个")
                
                return True
            else:
                print(f"❌ 查找失败: {result.get('error')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

if __name__ == "__main__":
    import sys
    
    user_id = sys.argv[1] if len(sys.argv) > 1 else "0220280ee0021000"
    limit = int(sys.argv[2]) if len(sys.argv) > 2 else 20
    
    success = test_user_similarity(user_id, limit)
    exit(0 if success else 1)
