# 批量猫咪初始化指南

本文档说明如何为用户 `0220280ee0021000` 批量初始化三只猫的特征向量，每只猫使用100张真实图片。

## 📊 配置信息

- **用户ID**: `0220280ee0021000`
- **猫咪数量**: 3只
- **每只猫图片数量**: 100张
- **总图片数量**: 300张

### 猫咪信息

| 猫咪名称 | Cat ID | 可用图片数量 |
|---------|--------|-------------|
| 小花 | `f3ce1b02b5f1a8b9d31000` | 518张 |
| 小黑 | `f3ce1b02b2c1d755421000` | 394张 |
| 小白 | `f3ce1b02b40e9477c21000` | 600张 |

## 📁 生成的文件

1. **`cats_batch_init_config.json`** - 完整的100张图片配置文件
2. **`cats_test_config.json`** - 测试用的5张图片配置文件
3. **`scripts/batch_init_cats.py`** - 批量初始化脚本
4. **`test_user_similarity.py`** - 用户相似度测试脚本

## 🚀 使用方法

### 步骤1: 清理现有数据（可选）

```bash
cd /home/<USER>/animsi/aby/server/caby_ai
python scripts/cleanup_test_data.py
```

### 步骤2: 测试批量初始化（推荐先测试）

使用5张图片进行测试：

```bash
python scripts/batch_init_cats.py --config cats_test_config.json
```

### 步骤3: 验证测试结果

```bash
python test_user_similarity.py 0220280ee0021000
```

### 步骤4: 完整批量初始化

⚠️ **注意**: 这将处理300张图片，预计需要5-10分钟

```bash
python scripts/batch_init_cats.py --config cats_batch_init_config.json
```

### 步骤5: 验证完整结果

```bash
python test_user_similarity.py 0220280ee0021000 50
```

## 📋 预期结果

### 测试阶段（15张图片）
- 小花: 5个特征向量 (`f3ce1b02b5f1a8b9d31000_001` 到 `_005`)
- 小黑: 5个特征向量 (`f3ce1b02b2c1d755421000_001` 到 `_005`)
- 小白: 5个特征向量 (`f3ce1b02b40e9477c21000_001` 到 `_005`)

### 完整初始化（300张图片）
- 小花: 100个特征向量 (`f3ce1b02b5f1a8b9d31000_001` 到 `_100`)
- 小黑: 100个特征向量 (`f3ce1b02b2c1d755421000_001` 到 `_100`)
- 小白: 100个特征向量 (`f3ce1b02b40e9477c21000_001` 到 `_100`)

## 🔧 特征

### 批量初始化脚本特点
- ✅ 支持多张图片批量处理
- ✅ 自动图片格式转换和大小调整
- ✅ 进度显示和错误处理
- ✅ 为每张图片生成唯一ID
- ✅ 支持断点续传（失败的图片可以重新处理）

### ID命名规则
- 基础ID: 用户指定的固定ID
- 完整ID: `{基础ID}_{序号:03d}`
- 示例: `f3ce1b02b5f1a8b9d31000_001`, `f3ce1b02b5f1a8b9d31000_002`

## 🔍 监控和调试

### 查看处理进度
脚本会显示：
- 当前处理的猫咪和图片
- 每张图片的处理结果
- 每10张图片的总体进度
- 最终成功/失败统计

### 常见问题

1. **图片文件不存在**
   - 检查图片路径是否正确
   - 确认图片文件权限

2. **API请求失败**
   - 检查服务是否正常运行
   - 验证API密钥是否正确

3. **处理速度慢**
   - 正常现象，每张图片需要提取特征向量
   - 可以调整延迟时间（当前0.1秒）

### 验证命令

```bash
# 检查服务状态
docker ps | grep caby

# 检查Qdrant集合
curl -H "api-key: 735b4cbbb3c9a07747e87f170da2773ea9238ddc12bc0f18fbf68436d1f498df" \
     http://localhost:6333/collections

# 测试相似度搜索
python test_user_similarity.py 0220280ee0021000 20
```

## ⚠️ 注意事项

1. **时间估算**: 300张图片预计需要5-10分钟处理时间
2. **存储空间**: 确保有足够的磁盘空间存储特征向量
3. **网络稳定**: 保持网络连接稳定，避免中断
4. **服务状态**: 确保caby_ai和caby_vision服务正常运行
5. **备份**: 建议在大批量操作前备份现有数据

## 🎯 优势

使用100张真实图片初始化的优势：
- 🎯 更准确的特征表示
- 🔍 更好的相似度区分
- 🚀 更稳定的识别性能
- 📈 更丰富的特征多样性

## 📞 故障排除

如果遇到问题：
1. 查看服务日志: `docker logs caby_ai_service --tail 50`
2. 检查Qdrant状态: `docker logs qdrant_service --tail 50`
3. 验证图片文件存在性
4. 重新运行失败的部分
