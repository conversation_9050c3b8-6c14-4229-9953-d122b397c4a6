#!/usr/bin/env python3
"""
调试Qdrant响应，检查相似度分数
"""

import requests
import json
import base64
import os

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    try:
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
            base64_string = base64.b64encode(image_data).decode('utf-8')
            return base64_string
    except Exception as e:
        print(f"❌ 处理图片失败: {e}")
        return None

def debug_qdrant_response():
    """调试Qdrant响应"""
    print("🔍 调试Qdrant响应和相似度分数")
    print("=" * 60)
    
    # 1. 先通过caby_ai获取特征向量
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    user_id = "0220280ee0021000"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 使用一张真实的猫咪图片
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    image_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    
    try:
        with open(annotations_path, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
    except Exception as e:
        print(f"❌ 加载标注文件失败: {e}")
        return False
    
    # 找一张小花的图片
    test_image_path = None
    for image_name, data in annotations.items():
        if data.get('category') == '小花':
            test_path = os.path.join(image_dir, image_name)
            if os.path.exists(test_path):
                test_image_path = test_path
                print(f"使用测试图片: {image_name}")
                break
    
    if not test_image_path:
        print("❌ 找不到测试图片")
        return False
    
    # 转换图片为base64
    image_base64 = image_to_base64(test_image_path)
    if not image_base64:
        return False
    
    # 2. 通过caby_ai的test-similarity API获取结果
    print("\n1️⃣ 通过caby_ai API获取相似度结果...")
    
    request_data = {
        "user_id": user_id,
        "image_base64": image_base64,
        "limit": 3
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/test-similarity",
                               headers=headers, 
                               json=request_data,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ API响应成功")
            print(f"   原始响应: {json.dumps(result, indent=2)[:500]}...")
            
            if result.get('success') and result.get('results'):
                for i, item in enumerate(result['results'][:3], 1):
                    similarity = item.get('similarity', 'N/A')
                    payload = item.get('payload', {})
                    original_id = payload.get('original_id', 'N/A')
                    print(f"   结果{i}: ID={original_id[:30]}..., 相似度={similarity}")
            else:
                print(f"   ❌ API返回失败: {result}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False
    
    # 3. 直接通过caby_vision获取特征向量
    print("\n2️⃣ 直接通过caby_vision获取特征向量...")
    
    vision_url = "http://localhost:8001"
    vision_api_key = "bcfa6d713db19f5888cad77a84714b8563b3529f87be94a0ec53460e5e7d1602"
    
    vision_headers = {
        "Authorization": f"Bearer {vision_api_key}",
        "Content-Type": "application/json"
    }
    
    vision_request = {
        "image": image_base64,
        "user_id": user_id,
        "task": "extract_features"
    }
    
    try:
        response = requests.post(f"{vision_url}/featured/extract", 
                               headers=vision_headers, 
                               json=vision_request,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('results'):
                features = result['results'].get('features', [])
                print(f"   ✅ 特征提取成功，维度: {len(features)}")
                print(f"   前5个特征值: {features[:5]}")
                
                # 4. 直接调用Qdrant API进行搜索
                print("\n3️⃣ 直接调用Qdrant API进行搜索...")
                
                qdrant_url = "http://localhost:6333"
                qdrant_headers = {
                    "api-key": "735b4cbbb3c9a07747e87f170da2773ea9238ddc12bc0f18fbf68436d1f498df",
                    "Content-Type": "application/json"
                }
                
                # 转换为float32
                features_32 = [float(f) for f in features]
                
                qdrant_request = {
                    "vector": features_32,
                    "limit": 3,
                    "with_payload": True,
                    "filter": {
                        "must": [
                            {
                                "key": "user_id",
                                "match": {"value": user_id}
                            }
                        ]
                    }
                }
                
                collection_name = f"cat_features_{user_id}"
                
                try:
                    response = requests.post(f"{qdrant_url}/collections/{collection_name}/points/search",
                                           headers=qdrant_headers,
                                           json=qdrant_request,
                                           timeout=30)
                    
                    if response.status_code == 200:
                        result = response.json()
                        print(f"   ✅ Qdrant搜索成功")
                        print(f"   原始Qdrant响应: {json.dumps(result, indent=2)[:800]}...")
                        
                        if 'result' in result:
                            for i, item in enumerate(result['result'][:3], 1):
                                score = item.get('score', 'N/A')
                                payload = item.get('payload', {})
                                original_id = payload.get('original_id', 'N/A')
                                print(f"   Qdrant结果{i}: ID={original_id[:30]}..., 原始分数={score}")
                        
                        return True
                    else:
                        print(f"   ❌ Qdrant HTTP错误: {response.status_code} - {response.text}")
                        return False
                except Exception as e:
                    print(f"   ❌ Qdrant请求异常: {e}")
                    return False
            else:
                print(f"   ❌ 特征提取失败: {result}")
                return False
        else:
            print(f"   ❌ Vision HTTP错误: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ Vision请求异常: {e}")
        return False

if __name__ == "__main__":
    success = debug_qdrant_response()
    exit(0 if success else 1)
