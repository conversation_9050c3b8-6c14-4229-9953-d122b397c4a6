#!/usr/bin/env python3
"""
验证影子模式是否真的在使用向量相似度，而不是三分类结果
"""

import requests
import json
import base64
import os
from PIL import Image
import io

def create_test_image(color, text):
    """创建一个测试图片"""
    img = Image.new('RGB', (224, 224), color=color)
    return img

def image_to_base64(image):
    """将PIL图像转换为base64"""
    buffer = io.BytesIO()
    image.save(buffer, format='JPEG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_vision_featured_extract():
    """直接测试caby_vision的featured/extract端点"""
    print("🔍 直接测试caby_vision的featured/extract端点")
    print("=" * 60)
    
    # 创建两个不同的测试图片
    red_image = create_test_image((255, 0, 0), "Red")
    blue_image = create_test_image((0, 0, 255), "Blue")
    
    red_base64 = image_to_base64(red_image)
    blue_base64 = image_to_base64(blue_image)
    
    vision_url = "http://localhost:8001"
    api_key = "bcfa6d713db19f5888cad77a84714b8563b3529f87be94a0ec53460e5e7d1602"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 测试红色图片
    print("\n1️⃣ 测试红色图片特征提取...")
    red_request = {
        "image": red_base64,
        "user_id": "test_user",
        "task": "extract_features"
    }

    print(f"   请求数据: task={red_request['task']}, user_id={red_request['user_id']}, image_size={len(red_base64)}")
    
    try:
        response = requests.post(f"{vision_url}/featured/extract", 
                               headers=headers, 
                               json=red_request,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('results'):
                features = result['results'].get('features', [])
                print(f"   ✅ 成功提取特征向量")
                print(f"   特征维度: {len(features)}")
                print(f"   前5个特征值: {features[:5]}")
                print(f"   特征向量范围: [{min(features):.4f}, {max(features):.4f}]")
                red_features = features
            else:
                print(f"   ❌ 特征提取失败: {result}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False
    
    # 测试蓝色图片
    print("\n2️⃣ 测试蓝色图片特征提取...")
    blue_request = {
        "image": blue_base64,
        "user_id": "test_user",
        "task": "extract_features"
    }

    print(f"   请求数据: task={blue_request['task']}, user_id={blue_request['user_id']}, image_size={len(blue_base64)}")
    
    try:
        response = requests.post(f"{vision_url}/featured/extract", 
                               headers=headers, 
                               json=blue_request,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and result.get('results'):
                features = result['results'].get('features', [])
                print(f"   ✅ 成功提取特征向量")
                print(f"   特征维度: {len(features)}")
                print(f"   前5个特征值: {features[:5]}")
                print(f"   特征向量范围: [{min(features):.4f}, {max(features):.4f}]")
                blue_features = features
            else:
                print(f"   ❌ 特征提取失败: {result}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False
    
    # 比较两个特征向量
    print("\n3️⃣ 比较特征向量...")
    if len(red_features) == len(blue_features):
        # 计算欧几里得距离
        import math
        distance = math.sqrt(sum((a - b) ** 2 for a, b in zip(red_features, blue_features)))
        print(f"   欧几里得距离: {distance:.4f}")
        
        # 计算余弦相似度
        def cosine_similarity(a, b):
            dot_product = sum(x * y for x, y in zip(a, b))
            magnitude_a = math.sqrt(sum(x * x for x in a))
            magnitude_b = math.sqrt(sum(x * x for x in b))
            if magnitude_a == 0 or magnitude_b == 0:
                return 0
            return dot_product / (magnitude_a * magnitude_b)
        
        similarity = cosine_similarity(red_features, blue_features)
        print(f"   余弦相似度: {similarity:.4f}")
        
        # 检查是否是真实的特征向量
        if distance > 0.001 and abs(similarity) < 0.99:
            print("   ✅ 特征向量确实不同，这是真实的特征提取")
            return True
        else:
            print("   ⚠️  特征向量过于相似，可能不是真实的特征提取")
            return False
    else:
        print(f"   ❌ 特征向量维度不匹配: {len(red_features)} vs {len(blue_features)}")
        return False

def test_shadow_mode_with_different_images():
    """使用不同图片测试影子模式是否真的在做向量比较"""
    print("\n🔍 测试影子模式是否真的在做向量比较")
    print("=" * 60)
    
    # 创建完全不同的图片
    red_image = create_test_image((255, 0, 0), "Red")
    green_image = create_test_image((0, 255, 0), "Green")
    
    red_base64 = image_to_base64(red_image)
    green_base64 = image_to_base64(green_image)
    
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    user_id = "0220280ee0021000"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    def test_image(image_base64, image_name):
        request_data = {
            "user_id": user_id,
            "image_base64": image_base64,
            "limit": 5
        }
        
        try:
            response = requests.post(f"{base_url}/api/v1/admin/test-similarity",
                                   headers=headers, 
                                   json=request_data,
                                   timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    results = result.get('results', [])
                    print(f"\n{image_name} 图片测试:")
                    print(f"   找到 {len(results)} 个相似结果")
                    
                    # 统计每个猫咪类别的结果数量
                    cat_counts = {'小花': 0, '小黑': 0, '小白': 0}
                    for item in results:
                        payload = item.get('payload', {})
                        original_id = payload.get('original_id', '')
                        
                        if 'f3ce1b02b5f1a8b9d31000' in original_id:
                            cat_counts['小花'] += 1
                        elif 'f3ce1b02b2c1d755421000' in original_id:
                            cat_counts['小黑'] += 1
                        elif 'f3ce1b02b40e9477c21000' in original_id:
                            cat_counts['小白'] += 1
                    
                    print(f"   结果分布: 小花={cat_counts['小花']}, 小黑={cat_counts['小黑']}, 小白={cat_counts['小白']}")
                    return cat_counts
                else:
                    print(f"   ❌ 查找失败: {result.get('error')}")
                    return None
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                return None
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            return None
    
    # 测试两个不同的图片
    red_counts = test_image(red_base64, "红色")
    green_counts = test_image(green_base64, "绿色")
    
    if red_counts and green_counts:
        # 检查结果是否相同
        if red_counts == green_counts:
            print("\n⚠️  两个完全不同的图片返回了相同的结果分布!")
            print("   这可能表明系统没有真正进行向量相似度比较")
            return False
        else:
            print("\n✅ 两个不同图片返回了不同的结果分布")
            print("   这表明系统确实在进行向量相似度比较")
            return True
    else:
        print("\n❌ 测试失败，无法获取有效结果")
        return False

def main():
    print("🔍 验证影子模式是否真的在使用向量相似度")
    print("=" * 80)
    
    # 测试1: 直接测试vision服务的特征提取
    vision_test = test_vision_featured_extract()
    
    # 测试2: 测试影子模式的向量比较
    shadow_test = test_shadow_mode_with_different_images()
    
    print("\n📊 测试总结")
    print("=" * 40)
    print(f"Vision特征提取测试: {'✅ 通过' if vision_test else '❌ 失败'}")
    print(f"影子模式向量比较测试: {'✅ 通过' if shadow_test else '❌ 失败'}")
    
    if vision_test and shadow_test:
        print("\n🎉 影子模式确实在使用真实的向量相似度比较!")
    elif vision_test and not shadow_test:
        print("\n⚠️  Vision服务能提取特征，但影子模式可能没有真正使用向量比较")
    else:
        print("\n❌ 影子模式可能没有正确工作")
    
    return vision_test and shadow_test

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
