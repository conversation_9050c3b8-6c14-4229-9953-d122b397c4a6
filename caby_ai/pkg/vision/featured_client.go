package vision

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

// FeaturedClient 特征提取客户端
type FeaturedClient struct {
	baseURL    string
	apiKey     string
	httpClient *http.Client
}

// FeaturedRequest 特征提取请求
type FeaturedRequest struct {
	Image  string `json:"image"`
	UserID string `json:"user_id"`
	Task   string `json:"task"`
}

// FeaturedResponse 特征提取响应
type FeaturedResponse struct {
	Success      bool      `json:"success"`
	Features     []float64 `json:"features"`
	FeatureDim   int       `json:"feature_dim"`
	ModelVersion string    `json:"model_version"`
	Device       string    `json:"device"`
	UserID       string    `json:"user_id"`
	Timestamp    string    `json:"timestamp"`
	Error        string    `json:"error,omitempty"`
}

// PredictWithFeaturesResponse 预测并返回特征的响应
type PredictWithFeaturesResponse struct {
	Success      bool      `json:"success"`
	PredictedCat string    `json:"predicted_cat"`
	Confidence   float64   `json:"confidence"`
	Features     []float64 `json:"features"`
	FeatureDim   int       `json:"feature_dim"`
	ModelVersion string    `json:"model_version"`
	Device       string    `json:"device"`
	UserID       string    `json:"user_id"`
	Timestamp    string    `json:"timestamp"`
	Error        string    `json:"error,omitempty"`
}

// NewFeaturedClient 创建新的特征提取客户端
func NewFeaturedClient(baseURL, apiKey string) *FeaturedClient {
	// 创建一个自定义的Transport，禁用代理以避免容器内部代理问题
	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment, // 使用环境变量的代理设置
	}

	// 对于内部服务通信，禁用代理
	if isInternalService(baseURL) {
		transport.Proxy = nil // 禁用代理
	}

	return &FeaturedClient{
		baseURL: baseURL,
		apiKey:  apiKey,
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
		},
	}
}

// SetTimeout 设置HTTP客户端超时时间
func (c *FeaturedClient) SetTimeout(timeout time.Duration) {
	c.httpClient.Timeout = timeout
}

// makeRequest 发送HTTP请求
func (c *FeaturedClient) makeRequest(method, endpoint string, request interface{}) ([]byte, error) {
	// 构建完整URL
	fullURL, err := url.JoinPath(c.baseURL, endpoint)
	if err != nil {
		return nil, fmt.Errorf("failed to build URL: %w", err)
	}

	// 序列化请求体
	var requestBody io.Reader
	if request != nil {
		jsonData, err := json.Marshal(request)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request: %w", err)
		}
		requestBody = bytes.NewBuffer(jsonData)
	}

	// 创建HTTP请求
	req, err := http.NewRequest(method, fullURL, requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	if c.apiKey != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.apiKey))
	}

	// 发送请求
	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP error %d: %s", resp.StatusCode, string(responseBody))
	}

	return responseBody, nil
}

// ExtractFeatures 提取特征向量
func (c *FeaturedClient) ExtractFeatures(imageBase64, userID string) (*FeaturedResponse, error) {
	request := &FeaturedRequest{
		Image:  imageBase64,
		UserID: userID,
		Task:   "extract_features",
	}

	responseBody, err := c.makeRequest("POST", "/featured/extract", request)
	if err != nil {
		return nil, fmt.Errorf("feature extraction request failed: %w", err)
	}

	var response FeaturedResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("failed to parse feature extraction response: %w", err)
	}

	return &response, nil
}

// PredictWithFeatures 预测并返回特征向量
func (c *FeaturedClient) PredictWithFeatures(imageBase64, userID string) (*PredictWithFeaturesResponse, error) {
	request := &FeaturedRequest{
		Image:  imageBase64,
		UserID: userID,
		Task:   "predict_with_features",
	}

	responseBody, err := c.makeRequest("POST", "/featured/extract", request)
	if err != nil {
		return nil, fmt.Errorf("prediction with features request failed: %w", err)
	}

	var response PredictWithFeaturesResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return nil, fmt.Errorf("failed to parse prediction with features response: %w", err)
	}

	return &response, nil
}

// HealthCheck 健康检查
func (c *FeaturedClient) HealthCheck() error {
	responseBody, err := c.makeRequest("GET", "/health", nil)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}

	var response map[string]interface{}
	if err := json.Unmarshal(responseBody, &response); err != nil {
		return fmt.Errorf("failed to parse health check response: %w", err)
	}

	if status, ok := response["status"].(string); !ok || status != "ok" {
		return fmt.Errorf("service is not healthy: %v", response)
	}

	return nil
}
