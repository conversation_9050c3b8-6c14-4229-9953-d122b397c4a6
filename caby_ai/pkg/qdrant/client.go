package qdrant

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/models"
)

// QdrantClient wraps the Qdrant HTTP client
type QdrantClient struct {
	httpClient *http.Client
	cfg        config.QdrantConfig
	baseURL    string
}

// NewClient creates a new Qdrant client
func NewClient(cfg config.QdrantConfig) (*QdrantClient, error) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	baseURL := fmt.Sprintf("%s://%s", cfg.Scheme, cfg.Host)

	log.Printf("Initializing Qdrant client for %s", baseURL)

	return &QdrantClient{
		httpClient: client,
		cfg:        cfg,
		baseURL:    baseURL,
	}, nil
}

// makeRequest makes an authenticated HTTP request to Qdrant
func (qc *QdrantClient) makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader

	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		// 添加调试日志
		log.Printf("Qdrant request JSON: %s", string(jsonData))
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, qc.baseURL+endpoint, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication header
	if qc.cfg.ApiKey != "" {
		req.Header.Set("api-key", qc.cfg.ApiKey)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return qc.httpClient.Do(req)
}

// CollectionInfo represents Qdrant collection information
type CollectionInfo struct {
	Name   string `json:"name"`
	Status string `json:"status"`
}

// EnsureCollection checks if the required collection exists in Qdrant and creates it if not
func (qc *QdrantClient) EnsureCollection(ctx context.Context) error {
	collectionName := "record_analysis"

	// Check if collection exists
	resp, err := qc.makeRequest("GET", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		log.Printf("Collection '%s' already exists", collectionName)
		return nil
	}

	if resp.StatusCode != 404 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected response when checking collection: %d - %s", resp.StatusCode, string(body))
	}

	// Create collection
	collectionConfig := map[string]interface{}{
		"vectors": map[string]interface{}{
			"size":     384, // 使用适合的向量维度
			"distance": "Cosine",
		},
	}

	resp, err = qc.makeRequest("PUT", fmt.Sprintf("/collections/%s", collectionName), collectionConfig)
	if err != nil {
		return fmt.Errorf("failed to create collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to create collection '%s': %d - %s", collectionName, resp.StatusCode, string(body))
	}

	log.Printf("Created Qdrant collection: %s", collectionName)
	return nil
}

// Point represents a Qdrant point (document)
type Point struct {
	ID      string                 `json:"id"`
	Vector  []float32              `json:"vector"`
	Payload map[string]interface{} `json:"payload"`
}

// UpsertPoints represents a batch upsert request
type UpsertPoints struct {
	Points []Point `json:"points"`
}

// SaveAnalysis saves the RecordAnalysis object to Qdrant
func (qc *QdrantClient) SaveAnalysis(ctx context.Context, analysis *models.RecordAnalysis) error {
	collectionName := "record_analysis"

	// 创建向量 (这里应该使用实际的嵌入向量，暂时使用占位符)
	vector := make([]float32, 384)
	for i := range vector {
		vector[i] = 0.1 // 占位符向量
	}

	point := Point{
		ID:     analysis.VideoID, // 使用 VideoID 作为唯一标识
		Vector: vector,
		Payload: map[string]interface{}{
			"video_id":       analysis.VideoID,
			"animal_id":      analysis.AnimalID,
			"cat_confidence": analysis.CatConfidence,
			"behavior_type":  analysis.BehaviorType,
			"is_abnormal":    analysis.IsAbnormal,
			"abnormal_type":  analysis.AbnormalType,
			"abnormal_prob":  analysis.AbnormalProb,
			"ai_results":     analysis.AiResults, // 注意字段名
			"created_at":     analysis.CreatedAt,
			"updated_at":     analysis.UpdatedAt,
		},
	}

	upsertReq := UpsertPoints{
		Points: []Point{point},
	}

	resp, err := qc.makeRequest("PUT", fmt.Sprintf("/collections/%s/points", collectionName), upsertReq)
	if err != nil {
		return fmt.Errorf("failed to upsert point: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to save analysis: %d - %s", resp.StatusCode, string(body))
	}

	log.Printf("Successfully saved analysis with ID: %s", analysis.VideoID)
	return nil
}

// SearchRequest represents a search request to Qdrant
type SearchRequest struct {
	Vector      []float32              `json:"vector"`
	Limit       int                    `json:"limit"`
	WithPayload bool                   `json:"with_payload"`
	Filter      map[string]interface{} `json:"filter,omitempty"`
}

// SearchResponse represents a search response from Qdrant
type SearchResponse struct {
	Result []SearchResult `json:"result"`
}

// SearchResult represents a single search result
type SearchResult struct {
	ID      string                 `json:"id"`
	Score   float64                `json:"score"`
	Payload map[string]interface{} `json:"payload"`
}

// SearchSimilar searches for similar analyses in Qdrant
func (qc *QdrantClient) SearchSimilar(ctx context.Context, vector []float32, limit int) ([]SearchResult, error) {
	collectionName := "record_analysis"

	searchReq := SearchRequest{
		Vector:      vector,
		Limit:       limit,
		WithPayload: true,
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/search", collectionName), searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to search: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("search failed: %d - %s", resp.StatusCode, string(body))
	}

	var searchResp SearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("failed to decode search response: %w", err)
	}

	return searchResp.Result, nil
}

// HealthCheck checks if Qdrant is healthy
func (qc *QdrantClient) HealthCheck(ctx context.Context) error {
	resp, err := qc.makeRequest("GET", "/", nil)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("qdrant is unhealthy: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// CollectionExists 检查集合是否存在
func (qc *QdrantClient) CollectionExists(ctx context.Context, collectionName string) (bool, error) {
	resp, err := qc.makeRequest("GET", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return false, fmt.Errorf("failed to check collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil
	}

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return false, fmt.Errorf("failed to check collection: %d - %s", resp.StatusCode, string(body))
	}

	return true, nil
}

// CreateCollection 创建集合
func (qc *QdrantClient) CreateCollection(ctx context.Context, collectionName string, vectorSize int) error {
	createReq := map[string]interface{}{
		"vectors": map[string]interface{}{
			"size":     vectorSize,
			"distance": "Cosine",
		},
	}

	resp, err := qc.makeRequest("PUT", fmt.Sprintf("/collections/%s", collectionName), createReq)
	if err != nil {
		return fmt.Errorf("failed to create collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 409 { // 409 = already exists
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to create collection: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// DeleteVector 删除向量
func (qc *QdrantClient) DeleteVector(ctx context.Context, collectionName string, vectorID string) error {
	deleteReq := map[string]interface{}{
		"points": []string{vectorID},
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/delete", collectionName), deleteReq)
	if err != nil {
		return fmt.Errorf("failed to delete vector: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to delete vector: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// GetUserCatFeaturesCollection 获取用户猫咪特征集合名称
func (qc *QdrantClient) GetUserCatFeaturesCollection(userID string) string {
	return fmt.Sprintf("cat_features_%s", userID)
}

// EnsureUserCatFeaturesCollection 确保用户的猫咪特征集合存在
func (qc *QdrantClient) EnsureUserCatFeaturesCollection(ctx context.Context, userID string, vectorSize int) error {
	collectionName := qc.GetUserCatFeaturesCollection(userID)

	// 检查集合是否存在
	resp, err := qc.makeRequest("GET", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		log.Printf("User cat features collection '%s' already exists", collectionName)
		return nil
	}

	if resp.StatusCode != 404 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected response when checking collection: %d - %s", resp.StatusCode, string(body))
	}

	// 创建集合
	return qc.CreateCollection(ctx, collectionName, vectorSize)
}

// UpsertCatFeature 插入或更新猫咪特征向量
func (qc *QdrantClient) UpsertCatFeature(ctx context.Context, userID string, catID string, vector []float64, payload map[string]interface{}) error {
	collectionName := qc.GetUserCatFeaturesCollection(userID)

	// 确保payload包含必要的用户和猫咪信息
	if payload == nil {
		payload = make(map[string]interface{})
	}
	payload["user_id"] = userID
	payload["cat_id"] = catID

	// 生成唯一ID：cat_id + timestamp
	pointID := fmt.Sprintf("%s_%d", catID, time.Now().UnixNano())

	return qc.UpsertVector(ctx, collectionName, pointID, vector, payload)
}

// SearchSimilarCatFeatures 在用户范围内搜索相似的猫咪特征
func (qc *QdrantClient) SearchSimilarCatFeatures(ctx context.Context, userID string, vector []float64, limit int) ([]SearchResult, error) {
	collectionName := qc.GetUserCatFeaturesCollection(userID)

	// 添加用户过滤条件
	return qc.SearchWithFilter(ctx, collectionName, vector, limit, map[string]interface{}{
		"user_id": userID,
	})
}

// UpsertVector 插入或更新单个向量 - 影子模式专用
func (qc *QdrantClient) UpsertVector(ctx context.Context, collectionName string, id string, vector []float64, payload map[string]interface{}) error {
	// 转换float64到float32
	vector32 := make([]float32, len(vector))
	for i, v := range vector {
		vector32[i] = float32(v)
	}

	point := Point{
		ID:      id,
		Vector:  vector32,
		Payload: payload,
	}

	// 使用batch格式
	upsertReq := map[string]interface{}{
		"batch": map[string]interface{}{
			"ids":      []string{point.ID},
			"vectors":  [][]float32{point.Vector},
			"payloads": []map[string]interface{}{point.Payload},
		},
	}

	resp, err := qc.makeRequest("PUT", fmt.Sprintf("/collections/%s/points", collectionName), upsertReq)
	if err != nil {
		return fmt.Errorf("failed to upsert vector: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to upsert vector: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// SearchWithFilter 带过滤条件的向量搜索
func (qc *QdrantClient) SearchWithFilter(ctx context.Context, collectionName string, vector []float64, limit int, filter map[string]interface{}) ([]SearchResult, error) {
	// 转换float64到float32
	vector32 := make([]float32, len(vector))
	for i, v := range vector {
		vector32[i] = float32(v)
	}

	searchReq := SearchRequest{
		Vector:      vector32,
		Limit:       limit,
		WithPayload: true,
		Filter: map[string]interface{}{
			"must": []map[string]interface{}{
				{
					"key":   "user_id",
					"match": map[string]interface{}{"value": filter["user_id"]},
				},
			},
		},
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/search", collectionName), searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to search vectors with filter: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to search vectors with filter: %d - %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read search response: %w", err)
	}

	var searchResp SearchResponse
	if err := json.Unmarshal(body, &searchResp); err != nil {
		return nil, fmt.Errorf("failed to parse search response: %w", err)
	}

	return searchResp.Result, nil
}

// Search 搜索相似向量 - 影子模式专用
func (qc *QdrantClient) Search(ctx context.Context, collectionName string, vector []float64, limit int) ([]SearchResult, error) {
	// 转换float64到float32
	vector32 := make([]float32, len(vector))
	for i, v := range vector {
		vector32[i] = float32(v)
	}

	searchReq := SearchRequest{
		Vector:      vector32,
		Limit:       limit,
		WithPayload: true,
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/search", collectionName), searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to search vectors: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to search vectors: %d - %s", resp.StatusCode, string(body))
	}

	var searchResp SearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("failed to decode search response: %w", err)
	}

	return searchResp.Result, nil
}
