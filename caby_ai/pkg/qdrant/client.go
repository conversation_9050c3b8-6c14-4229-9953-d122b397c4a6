package qdrant

import (
	"bytes"
	"context"
	"crypto/sha256"
	"crypto/tls"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"caby-ai/config"
	"caby-ai/pkg/models"
)

// stringToUint64 converts a string ID to a uint64 using SHA256 hash
func stringToUint64(s string) uint64 {
	h := sha256.Sum256([]byte(s))
	return binary.BigEndian.Uint64(h[:8])
}

// QdrantClient wraps the Qdrant HTTP client
type QdrantClient struct {
	httpClient *http.Client
	cfg        config.QdrantConfig
	baseURL    string
}

// NewClient creates a new Qdrant client
func NewClient(cfg config.QdrantConfig) (*QdrantClient, error) {
	// 创建HTTP客户端，如果是HTTPS则跳过证书验证
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 如果是HTTPS，配置跳过证书验证
	if cfg.Scheme == "https" {
		client.Transport = &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}
		log.Printf("Configured HTTPS client with InsecureSkipVerify=true")
	}

	baseURL := fmt.Sprintf("%s://%s", cfg.Scheme, cfg.Host)

	log.Printf("Initializing Qdrant client for %s", baseURL)

	return &QdrantClient{
		httpClient: client,
		cfg:        cfg,
		baseURL:    baseURL,
	}, nil
}

// makeRequest makes an authenticated HTTP request to Qdrant
func (qc *QdrantClient) makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader

	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		// 添加调试日志
		log.Printf("Qdrant request JSON: %s", string(jsonData))
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, qc.baseURL+endpoint, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication header
	if qc.cfg.ApiKey != "" {
		req.Header.Set("api-key", qc.cfg.ApiKey)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return qc.httpClient.Do(req)
}

// CollectionInfo represents Qdrant collection information
type CollectionInfo struct {
	Name   string `json:"name"`
	Status string `json:"status"`
}

// EnsureCollection 已废弃 - 现在使用用户特定的集合
// 保留此方法以避免破坏现有代码，但不执行任何操作
func (qc *QdrantClient) EnsureCollection(ctx context.Context) error {
	log.Printf("EnsureCollection called but deprecated - using user-specific collections instead")
	return nil
}

// Point represents a Qdrant point (document)
type Point struct {
	ID      string                 `json:"id"`
	Vector  []float32              `json:"vector"`
	Payload map[string]interface{} `json:"payload"`
}

// UpsertPoints represents a batch upsert request
type UpsertPoints struct {
	Points []Point `json:"points"`
}

// SaveAnalysis 已废弃 - 分析结果现在只存储在MySQL中
// 保留此方法以避免破坏现有代码，但不执行任何操作
func (qc *QdrantClient) SaveAnalysis(ctx context.Context, analysis *models.RecordAnalysis) error {
	log.Printf("SaveAnalysis called but deprecated - analysis results are now stored only in MySQL")
	return nil
}

// SearchRequest represents a search request to Qdrant
type SearchRequest struct {
	Vector      []float32              `json:"vector"`
	Limit       int                    `json:"limit"`
	WithPayload bool                   `json:"with_payload"`
	Filter      map[string]interface{} `json:"filter,omitempty"`
}

// SearchResponse represents a search response from Qdrant
type SearchResponse struct {
	Result []SearchResult `json:"result"`
}

// SearchResult represents a single search result
type SearchResult struct {
	ID      interface{}            `json:"id"` // 可以是string或uint64
	Score   float64                `json:"score"`
	Payload map[string]interface{} `json:"payload"`
}

// SearchSimilar 已废弃 - 现在使用用户特定的特征向量搜索
// 保留此方法以避免破坏现有代码，但不执行任何操作
func (qc *QdrantClient) SearchSimilar(ctx context.Context, vector []float32, limit int) ([]SearchResult, error) {
	log.Printf("SearchSimilar called but deprecated - use user-specific feature vector search instead")
	return []SearchResult{}, nil
}

// HealthCheck checks if Qdrant is healthy
func (qc *QdrantClient) HealthCheck(ctx context.Context) error {
	resp, err := qc.makeRequest("GET", "/", nil)
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("qdrant is unhealthy: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// CollectionExists 检查集合是否存在
func (qc *QdrantClient) CollectionExists(ctx context.Context, collectionName string) (bool, error) {
	resp, err := qc.makeRequest("GET", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return false, fmt.Errorf("failed to check collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return false, nil
	}

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return false, fmt.Errorf("failed to check collection: %d - %s", resp.StatusCode, string(body))
	}

	return true, nil
}

// CreateCollection 创建集合
func (qc *QdrantClient) CreateCollection(ctx context.Context, collectionName string, vectorSize int) error {
	createReq := map[string]interface{}{
		"vectors": map[string]interface{}{
			"size":     vectorSize,
			"distance": "Cosine",
		},
	}

	resp, err := qc.makeRequest("PUT", fmt.Sprintf("/collections/%s", collectionName), createReq)
	if err != nil {
		return fmt.Errorf("failed to create collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 && resp.StatusCode != 409 { // 409 = already exists
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to create collection: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// DeleteVector 删除向量
func (qc *QdrantClient) DeleteVector(ctx context.Context, collectionName string, vectorID string) error {
	deleteReq := map[string]interface{}{
		"points": []string{vectorID},
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/delete", collectionName), deleteReq)
	if err != nil {
		return fmt.Errorf("failed to delete vector: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to delete vector: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// GetUserCatFeaturesCollection 获取用户猫咪特征集合名称
func (qc *QdrantClient) GetUserCatFeaturesCollection(userID string) string {
	return fmt.Sprintf("cat_features_%s", userID)
}

// EnsureUserCatFeaturesCollection 确保用户的猫咪特征集合存在
func (qc *QdrantClient) EnsureUserCatFeaturesCollection(ctx context.Context, userID string, vectorSize int) error {
	collectionName := qc.GetUserCatFeaturesCollection(userID)

	// 检查集合是否存在
	resp, err := qc.makeRequest("GET", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return fmt.Errorf("failed to check collection existence: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 200 {
		log.Printf("User cat features collection '%s' already exists", collectionName)
		return nil
	}

	if resp.StatusCode != 404 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("unexpected response when checking collection: %d - %s", resp.StatusCode, string(body))
	}

	// 创建集合
	return qc.CreateCollection(ctx, collectionName, vectorSize)
}

// UpsertCatFeature 插入或更新猫咪特征向量
func (qc *QdrantClient) UpsertCatFeature(ctx context.Context, userID string, catID string, vector []float64, payload map[string]interface{}) error {
	collectionName := qc.GetUserCatFeaturesCollection(userID)

	// 确保payload包含必要的用户和猫咪信息
	if payload == nil {
		payload = make(map[string]interface{})
	}
	payload["user_id"] = userID
	payload["cat_id"] = catID

	// 生成唯一ID：cat_id + timestamp
	pointID := fmt.Sprintf("%s_%d", catID, time.Now().UnixNano())

	return qc.UpsertVector(ctx, collectionName, pointID, vector, payload)
}

// SearchSimilarCatFeatures 在用户范围内搜索相似的猫咪特征
func (qc *QdrantClient) SearchSimilarCatFeatures(ctx context.Context, userID string, vector []float64, limit int) ([]SearchResult, error) {
	collectionName := qc.GetUserCatFeaturesCollection(userID)

	// 添加用户过滤条件
	return qc.SearchWithFilter(ctx, collectionName, vector, limit, map[string]interface{}{
		"user_id": userID,
	})
}

// UpsertVector 插入或更新单个向量 - 影子模式专用
func (qc *QdrantClient) UpsertVector(ctx context.Context, collectionName string, id string, vector []float64, payload map[string]interface{}) error {
	// 转换float64到float32
	vector32 := make([]float32, len(vector))
	for i, v := range vector {
		vector32[i] = float32(v)
	}

	// 将字符串ID转换为数字ID
	numericID := stringToUint64(id)

	// 在payload中添加原始字符串ID
	extendedPayload := make(map[string]interface{})
	for k, v := range payload {
		extendedPayload[k] = v
	}
	extendedPayload["original_id"] = id

	// 使用Qdrant v1.7.4的记录格式，使用数字ID
	upsertReq := map[string]interface{}{
		"points": []map[string]interface{}{
			{
				"id":      numericID,
				"vector":  vector32,
				"payload": extendedPayload,
			},
		},
	}

	resp, err := qc.makeRequest("PUT", fmt.Sprintf("/collections/%s/points", collectionName), upsertReq)
	if err != nil {
		return fmt.Errorf("failed to upsert vector: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to upsert vector: %d - %s", resp.StatusCode, string(body))
	}

	return nil
}

// SearchWithFilter 带过滤条件的向量搜索
func (qc *QdrantClient) SearchWithFilter(ctx context.Context, collectionName string, vector []float64, limit int, filter map[string]interface{}) ([]SearchResult, error) {
	// 转换float64到float32
	vector32 := make([]float32, len(vector))
	for i, v := range vector {
		vector32[i] = float32(v)
	}

	searchReq := SearchRequest{
		Vector:      vector32,
		Limit:       limit,
		WithPayload: true,
		Filter: map[string]interface{}{
			"must": []map[string]interface{}{
				{
					"key":   "user_id",
					"match": map[string]interface{}{"value": filter["user_id"]},
				},
			},
		},
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/search", collectionName), searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to search vectors with filter: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to search vectors with filter: %d - %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read search response: %w", err)
	}

	var searchResp SearchResponse
	if err := json.Unmarshal(body, &searchResp); err != nil {
		return nil, fmt.Errorf("failed to parse search response: %w", err)
	}

	return searchResp.Result, nil
}

// Search 搜索相似向量 - 影子模式专用
func (qc *QdrantClient) Search(ctx context.Context, collectionName string, vector []float64, limit int) ([]SearchResult, error) {
	// 转换float64到float32
	vector32 := make([]float32, len(vector))
	for i, v := range vector {
		vector32[i] = float32(v)
	}

	searchReq := SearchRequest{
		Vector:      vector32,
		Limit:       limit,
		WithPayload: true,
	}

	resp, err := qc.makeRequest("POST", fmt.Sprintf("/collections/%s/points/search", collectionName), searchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to search vectors: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("failed to search vectors: %d - %s", resp.StatusCode, string(body))
	}

	var searchResp SearchResponse
	if err := json.NewDecoder(resp.Body).Decode(&searchResp); err != nil {
		return nil, fmt.Errorf("failed to decode search response: %w", err)
	}

	return searchResp.Result, nil
}

// DeleteCollection 删除集合
func (qc *QdrantClient) DeleteCollection(ctx context.Context, collectionName string) error {
	resp, err := qc.makeRequest("DELETE", fmt.Sprintf("/collections/%s", collectionName), nil)
	if err != nil {
		return fmt.Errorf("failed to delete collection: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		// 集合不存在，认为删除成功
		return nil
	}

	if resp.StatusCode != 200 {
		return fmt.Errorf("unexpected response status: %d", resp.StatusCode)
	}

	return nil
}
