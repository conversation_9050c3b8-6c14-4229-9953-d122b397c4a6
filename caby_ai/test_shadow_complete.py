#!/usr/bin/env python3
"""
影子模式完整功能测试
"""

import requests
import json
import base64
from PIL import Image
import io

def create_test_image():
    """创建一个测试图片"""
    img = Image.new('RGB', (224, 224), color='red')
    buffer = io.BytesIO()
    img.save(buffer, format='JPEG')
    return base64.b64encode(buffer.getvalue()).decode()

def test_complete_shadow_mode():
    """完整的影子模式测试"""
    base_url = "http://localhost:8765"
    api_key = "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    user_id = "test_user_001"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("🚀 影子模式完整功能测试")
    print("=" * 60)
    
    # 1. 健康检查
    print("\n1️⃣ 健康检查...")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False
    
    # 2. 影子模式配置检查
    print("\n2️⃣ 影子模式配置检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/admin/shadow-config", headers=headers)
        if response.status_code == 200:
            config = response.json()
            shadow_config = config['config']
            print(f"✅ 影子模式配置:")
            print(f"   启用状态: {shadow_config['enabled']}")
            print(f"   相似度阈值: {shadow_config['similarity_threshold']}")
            print(f"   新猫阈值: {shadow_config['new_cat_threshold']}")
            print(f"   Top K: {shadow_config['top_k']}")
            
            if not shadow_config['enabled']:
                print("❌ 影子模式未启用")
                return False
        else:
            print(f"❌ 配置获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 配置获取异常: {e}")
        return False
    
    # 3. 猫咪特征初始化检查
    print("\n3️⃣ 检查猫咪特征初始化状态...")
    test_image = create_test_image()
    
    payload = {
        "user_id": user_id,
        "image_base64": test_image,
        "limit": 10
    }
    
    try:
        response = requests.post(f"{base_url}/api/v1/admin/test-similarity", 
                               headers=headers, 
                               json=payload)
        if response.status_code == 200:
            result = response.json()
            cat_count = len(result.get('results', []))
            print(f"✅ 找到 {cat_count} 个已初始化的猫咪特征")
            
            if cat_count == 0:
                print("⚠️  没有找到已初始化的猫咪特征，需要先运行初始化")
                return False
                
            # 显示前3个结果
            for i, cat in enumerate(result.get('results', [])[:3], 1):
                cat_id = cat.get('payload', {}).get('original_id', 'N/A')
                cat_name = cat.get('payload', {}).get('cat_name', 'Unknown')
                similarity = cat.get('score', 0)
                print(f"   {i}. {cat_name} (ID: {cat_id}) - 相似度: {similarity:.4f}")
                
        else:
            print(f"❌ 相似度测试失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 相似度测试异常: {e}")
        return False
    
    # 4. Vision服务健康检查
    print("\n4️⃣ Vision服务健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/vision/health", headers=headers)
        if response.status_code == 200:
            vision_health = response.json()
            print(f"✅ Vision服务健康:")
            print(f"   状态: {vision_health.get('status', 'N/A')}")
            print(f"   设备: {vision_health.get('details', {}).get('device', 'N/A')}")
            print(f"   模型已加载: {vision_health.get('details', {}).get('model_loaded', 'N/A')}")
            print(f"   类别数: {vision_health.get('details', {}).get('num_classes', 'N/A')}")
        else:
            print(f"❌ Vision健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Vision健康检查异常: {e}")
        return False
    
    # 5. 猫咪检测测试
    print("\n5️⃣ 猫咪检测测试...")
    try:
        # 将base64转换为文件对象
        image_data = base64.b64decode(test_image)
        files = {'image': ('test.jpg', io.BytesIO(image_data), 'image/jpeg')}
        data = {
            'task': 'predict',
            'return_features': 'false',
            'return_confidence': 'true'
        }

        # 只保留Authorization头，移除Content-Type让requests自动设置
        auth_headers = {"Authorization": headers["Authorization"]}

        response = requests.post(f"{base_url}/api/v1/vision/detect/cat",
                               headers=auth_headers,
                               data=data,
                               files=files)
        if response.status_code == 200:
            detection_result = response.json()
            print(f"✅ 猫咪检测成功:")
            print(f"   预测猫咪: {detection_result.get('predicted_cat', 'N/A')}")
            print(f"   置信度: {detection_result.get('confidence', 'N/A'):.4f}")
        else:
            print(f"❌ 猫咪检测失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ 猫咪检测异常: {e}")
        return False
    
    print("\n🎉 影子模式完整功能测试通过!")
    print("\n📋 测试总结:")
    print("   ✅ 服务健康检查")
    print("   ✅ 影子模式配置正确")
    print("   ✅ 猫咪特征已初始化")
    print("   ✅ Vision服务正常")
    print("   ✅ 猫咪检测功能正常")
    print("   ✅ 相似度搜索功能正常")
    
    return True

if __name__ == "__main__":
    success = test_complete_shadow_mode()
    if success:
        print("\n🚀 影子模式已准备就绪，可以开始处理实际的视频分析请求!")
    else:
        print("\n💥 影子模式测试失败，请检查配置和服务状态!")
        exit(1)
