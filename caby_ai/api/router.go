package api

import (
	"caby-ai/config"
	"caby-ai/pkg/auth"

	"github.com/gin-gonic/gin"
	// Import other necessary packages like analysis handler, clients, service
)

// Accept AnalysisHandler and VisionHandler in SetupRouter
func SetupRouter(cfg *config.Config, analysisHandler *AnalysisHandler, visionHandler *VisionHandler, adminHandler *AdminHandler) *gin.Engine {
	r := gin.Default()

	// Health check endpoint
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// API v1 group with service token authentication
	apiV1 := r.Group("/api/v1")
	apiV1.Use(auth.ServiceTokenAuth(cfg)) // Assuming auth middleware is correctly implemented
	{
		// Use the passed analysisHandler
		apiV1.POST("/analyze", analysisHandler.HandleAnalysisRequest) // Use the actual handler method

		// Vision相关端点
		vision := apiV1.Group("/vision")
		{
			vision.POST("/detect/cat", visionHandler.HandleCatDetection)
			vision.GET("/health", visionHandler.HandleHealthCheck)
		}

		// Admin相关端点（临时用于初始化）
		admin := apiV1.Group("/admin")
		{
			admin.POST("/init-cats", adminHandler.HandleInitCats)
			admin.POST("/test-similarity", adminHandler.HandleTestSimilarity)
			admin.GET("/shadow-config", adminHandler.HandleGetShadowConfig)
		}
	}

	return r
}
