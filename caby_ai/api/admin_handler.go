package api

import (
	"fmt"
	"log"
	"net/http"

	"caby-ai/config"
	"caby-ai/pkg/shadow"

	"github.com/gin-gonic/gin"
)

// AdminHandler 管理接口处理器
type AdminHandler struct {
	shadowService *shadow.Service
}

// NewAdminHandler 创建管理接口处理器
func NewAdminHandler(shadowService *shadow.Service) *AdminHandler {
	return &AdminHandler{
		shadowService: shadowService,
	}
}

// InitCatsRequest 初始化猫咪请求
type InitCatsRequest struct {
	UserID string `json:"user_id" binding:"required"`
	Cats   []struct {
		CatID       string `json:"cat_id" binding:"required"`
		Name        string `json:"name" binding:"required"`
		ImageBase64 string `json:"image_base64" binding:"required"`
	} `json:"cats" binding:"required"`
}

// BatchInitCatsRequest 批量初始化猫咪请求
type BatchInitCatsRequest struct {
	UserID string `json:"user_id" binding:"required"`
	Cats   []struct {
		CatID  string   `json:"cat_id" binding:"required"`
		Name   string   `json:"name" binding:"required"`
		Images []string `json:"images" binding:"required"` // base64编码的图片数组
	} `json:"cats" binding:"required"`
}

// InitCatsResponse 初始化猫咪响应
type InitCatsResponse struct {
	Success bool            `json:"success"`
	Message string          `json:"message"`
	Results []InitCatResult `json:"results,omitempty"`
	Error   string          `json:"error,omitempty"`
}

// InitCatResult 单个猫咪初始化结果
type InitCatResult struct {
	CatID           string `json:"cat_id"`
	Name            string `json:"name"`
	Success         bool   `json:"success"`
	ProcessedImages int    `json:"processed_images,omitempty"` // 成功处理的图片数量
	TotalImages     int    `json:"total_images,omitempty"`     // 总图片数量
	Error           string `json:"error,omitempty"`
}

// HandleInitCats 初始化三只猫的特征数据
// @Summary Initialize cat features for shadow mode
// @Description Initialize feature vectors for the three initial cats (小花, 小黑, 小白) using provided images
// @Tags Admin
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Service Token"
// @Param request body InitCatsRequest true "Cat initialization data"
// @Success 200 {object} InitCatsResponse "Cats initialized successfully"
// @Failure 400 {object} gin.H "Invalid input data"
// @Failure 401 {object} gin.H "Unauthorized"
// @Failure 500 {object} gin.H "Internal server error"
// @Router /api/v1/admin/init-cats [post]
func (h *AdminHandler) HandleInitCats(c *gin.Context) {
	var request InitCatsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format: " + err.Error()})
		return
	}

	log.Printf("Initializing cats for user %s with %d cats", request.UserID, len(request.Cats))

	// 检查影子模式是否启用
	if !h.shadowService.IsEnabled() {
		c.JSON(http.StatusBadRequest, InitCatsResponse{
			Success: false,
			Error:   "Shadow mode is not enabled",
		})
		return
	}

	ctx := c.Request.Context()
	results := make([]InitCatResult, 0, len(request.Cats))

	// 逐个初始化每只猫的特征
	for _, cat := range request.Cats {
		result := InitCatResult{
			CatID: cat.CatID,
			Name:  cat.Name,
		}

		log.Printf("Initializing features for cat %s", cat.CatID)

		err := h.shadowService.InitializeCatFeatures(ctx, request.UserID, cat.CatID, cat.ImageBase64)
		if err != nil {
			log.Printf("Failed to initialize features for cat %s: %v", cat.CatID, err)
			result.Success = false
			result.Error = err.Error()
		} else {
			log.Printf("Successfully initialized features for cat %s", cat.CatID)
			result.Success = true
		}

		results = append(results, result)
	}

	// 统计成功和失败的数量
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	response := InitCatsResponse{
		Success: successCount == len(request.Cats),
		Results: results,
	}

	if response.Success {
		response.Message = "All cats initialized successfully"
		log.Printf("Successfully initialized all %d cats for user %s", len(request.Cats), request.UserID)
	} else {
		response.Message = fmt.Sprintf("Initialized %d out of %d cats", successCount, len(request.Cats))
		log.Printf("Partially initialized cats for user %s: %d/%d successful", request.UserID, successCount, len(request.Cats))
	}

	c.JSON(http.StatusOK, response)
}

// HandleBatchInitCats 批量初始化猫咪特征
// @Summary Batch initialize cat features for shadow mode
// @Description Batch initialize feature vectors for cats using multiple images per cat
// @Tags Admin
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Service Token"
// @Param request body BatchInitCatsRequest true "Batch cat initialization data"
// @Success 200 {object} InitCatsResponse "Cats initialized successfully"
// @Failure 400 {object} gin.H "Invalid input data"
// @Failure 401 {object} gin.H "Unauthorized"
// @Failure 500 {object} gin.H "Internal server error"
// @Router /api/v1/admin/batch-init-cats [post]
func (h *AdminHandler) HandleBatchInitCats(c *gin.Context) {
	var request BatchInitCatsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format: " + err.Error()})
		return
	}

	log.Printf("Batch initializing cats for user %s with %d cats", request.UserID, len(request.Cats))

	// 检查影子模式是否启用
	if !h.shadowService.IsEnabled() {
		c.JSON(http.StatusBadRequest, InitCatsResponse{
			Success: false,
			Error:   "Shadow mode is not enabled",
		})
		return
	}

	ctx := c.Request.Context()
	results := make([]InitCatResult, 0, len(request.Cats))

	// 逐个初始化每只猫的特征
	for _, cat := range request.Cats {
		result := InitCatResult{
			CatID:       cat.CatID,
			Name:        cat.Name,
			TotalImages: len(cat.Images),
		}

		log.Printf("Batch initializing features for cat %s with %d images", cat.CatID, len(cat.Images))

		processedCount := 0
		for i, imageBase64 := range cat.Images {
			// 使用相同的cat_id，让同一只猫的所有图片作为一个集合存储
			err := h.shadowService.InitializeCatFeatures(ctx, request.UserID, cat.CatID, imageBase64)
			if err != nil {
				log.Printf("Failed to initialize features for cat %s image %d: %v", cat.CatID, i+1, err)
				continue
			}
			processedCount++

			// 每10张图片显示一次进度
			if (i+1)%10 == 0 {
				log.Printf("Processed %d/%d images for cat %s", i+1, len(cat.Images), cat.CatID)
			}
		}

		result.ProcessedImages = processedCount
		if processedCount > 0 {
			result.Success = true
			log.Printf("Successfully initialized %d/%d images for cat %s", processedCount, len(cat.Images), cat.CatID)
		} else {
			result.Success = false
			result.Error = "No images were successfully processed"
			log.Printf("Failed to initialize any images for cat %s", cat.CatID)
		}

		results = append(results, result)
	}

	// 统计成功和失败的数量
	successCount := 0
	totalProcessedImages := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
		totalProcessedImages += result.ProcessedImages
	}

	response := InitCatsResponse{
		Success: successCount > 0, // 只要有一只猫成功就算成功
		Results: results,
	}

	if successCount == len(request.Cats) {
		response.Message = fmt.Sprintf("All %d cats initialized successfully with %d total images", len(request.Cats), totalProcessedImages)
		log.Printf("Successfully batch initialized all %d cats for user %s with %d images", len(request.Cats), request.UserID, totalProcessedImages)
	} else {
		response.Message = fmt.Sprintf("Initialized %d out of %d cats with %d total images", successCount, len(request.Cats), totalProcessedImages)
		log.Printf("Partially batch initialized cats for user %s: %d/%d cats successful with %d images", request.UserID, successCount, len(request.Cats), totalProcessedImages)
	}

	c.JSON(http.StatusOK, response)
}

// TestSimilarityRequest 测试相似度请求
type TestSimilarityRequest struct {
	UserID      string `json:"user_id" binding:"required"`
	ImageBase64 string `json:"image_base64" binding:"required"`
	Limit       int    `json:"limit"`
}

// TestSimilarityResponse 测试相似度响应
type TestSimilarityResponse struct {
	Success bool               `json:"success"`
	Results []SimilarityResult `json:"results,omitempty"`
	Error   string             `json:"error,omitempty"`
}

// SimilarityResult 相似度结果
type SimilarityResult struct {
	CatID      string                 `json:"cat_id"`
	Similarity float64                `json:"similarity"`
	Payload    map[string]interface{} `json:"payload"`
}

// HandleTestSimilarity 测试相似度查找
// @Summary Test similarity search for debugging
// @Description Test similarity search against user's cat features for debugging purposes
// @Tags Admin
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Service Token"
// @Param request body TestSimilarityRequest true "Similarity test data"
// @Success 200 {object} TestSimilarityResponse "Similarity results"
// @Failure 400 {object} gin.H "Invalid input data"
// @Failure 401 {object} gin.H "Unauthorized"
// @Failure 500 {object} gin.H "Internal server error"
// @Router /api/v1/admin/test-similarity [post]
func (h *AdminHandler) HandleTestSimilarity(c *gin.Context) {
	var request TestSimilarityRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		log.Printf("Invalid request format: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format: " + err.Error()})
		return
	}

	// 设置默认limit
	if request.Limit <= 0 {
		request.Limit = 5
	}

	log.Printf("Testing similarity for user %s with limit %d", request.UserID, request.Limit)

	ctx := c.Request.Context()
	searchResults, err := h.shadowService.GetSimilarCats(ctx, request.UserID, request.ImageBase64, request.Limit)
	if err != nil {
		log.Printf("Failed to get similar cats: %v", err)
		c.JSON(http.StatusInternalServerError, TestSimilarityResponse{
			Success: false,
			Error:   err.Error(),
		})
		return
	}

	// 转换结果格式
	results := make([]SimilarityResult, 0, len(searchResults))
	for _, result := range searchResults {
		catID := "unknown"
		if id, ok := result.Payload["cat_id"].(string); ok {
			catID = id
		}

		results = append(results, SimilarityResult{
			CatID:      catID,
			Similarity: float64(result.Score),
			Payload:    result.Payload,
		})
	}

	log.Printf("Found %d similar cats for user %s", len(results), request.UserID)

	c.JSON(http.StatusOK, TestSimilarityResponse{
		Success: true,
		Results: results,
	})
}

// GetShadowConfigResponse 获取影子模式配置响应
type GetShadowConfigResponse struct {
	Success bool                     `json:"success"`
	Config  *config.ShadowModeConfig `json:"config,omitempty"`
	Error   string                   `json:"error,omitempty"`
}

// HandleGetShadowConfig 获取影子模式配置
// @Summary Get shadow mode configuration
// @Description Get current shadow mode configuration for debugging
// @Tags Admin
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer Service Token"
// @Success 200 {object} GetShadowConfigResponse "Shadow mode configuration"
// @Failure 401 {object} gin.H "Unauthorized"
// @Router /api/v1/admin/shadow-config [get]
func (h *AdminHandler) HandleGetShadowConfig(c *gin.Context) {
	config := h.shadowService.GetShadowModeConfig()

	c.JSON(http.StatusOK, GetShadowConfigResponse{
		Success: true,
		Config:  config,
	})
}
