#!/usr/bin/env python3
"""
基于annotations.json和图片目录生成猫咪初始化配置文件
"""

import json
import os
import random
from collections import defaultdict

def load_annotations(annotations_path):
    """加载annotations.json文件"""
    with open(annotations_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_cats(annotations):
    """分析猫咪数据"""
    cat_images = defaultdict(list)
    
    for image_name, data in annotations.items():
        category = data.get('category', '')
        if category in ['小黑', '小白', '小花']:
            cat_images[category].append(image_name)
    
    return cat_images

def select_representative_images(cat_images, images_per_cat=3):
    """为每只猫选择代表性图片"""
    selected = {}
    
    for cat_name, images in cat_images.items():
        if len(images) >= images_per_cat:
            # 随机选择指定数量的图片
            selected[cat_name] = random.sample(images, images_per_cat)
        else:
            # 如果图片不够，使用所有可用图片
            selected[cat_name] = images
    
    return selected

def generate_cat_ids():
    """生成猫咪ID"""
    import time
    base_timestamp = int(time.time() * 1000000)
    
    cat_ids = {
        '小黑': f"f3ce1b02b2c1d755421000_{base_timestamp}",
        '小白': f"f3ce1b02b40e9477c21000_{base_timestamp + 1}",
        '小花': f"f3ce1b02b5f1a8b9d31000_{base_timestamp + 2}"
    }
    
    return cat_ids

def create_init_config(selected_images, cat_ids, image_dir):
    """创建初始化配置"""
    config = []
    
    for cat_name, images in selected_images.items():
        cat_id = cat_ids[cat_name]
        
        # 为每只猫选择第一张图片作为主要特征图片
        main_image = images[0]
        image_path = os.path.join(image_dir, main_image)
        
        config.append({
            "cat_id": cat_id,
            "name": cat_name,
            "image_path": image_path,
            "alternative_images": [os.path.join(image_dir, img) for img in images[1:]]
        })
    
    return config

def main():
    # 配置路径
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    image_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    output_file = "cats_init_config.json"
    
    print("🐱 生成猫咪初始化配置文件")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(annotations_path):
        print(f"❌ annotations.json文件不存在: {annotations_path}")
        return 1
    
    if not os.path.exists(image_dir):
        print(f"❌ 图片目录不存在: {image_dir}")
        return 1
    
    # 加载annotations
    print("📄 加载annotations.json...")
    annotations = load_annotations(annotations_path)
    print(f"   总共 {len(annotations)} 个标注")
    
    # 分析猫咪数据
    print("\n🔍 分析猫咪数据...")
    cat_images = analyze_cats(annotations)
    
    for cat_name, images in cat_images.items():
        print(f"   {cat_name}: {len(images)} 张图片")
    
    if not cat_images:
        print("❌ 没有找到有效的猫咪数据")
        return 1
    
    # 选择代表性图片
    print("\n🎯 选择代表性图片...")
    selected_images = select_representative_images(cat_images, images_per_cat=3)
    
    for cat_name, images in selected_images.items():
        print(f"   {cat_name}: 选择了 {len(images)} 张图片")
        for i, img in enumerate(images, 1):
            print(f"      {i}. {img}")
    
    # 生成猫咪ID
    print("\n🆔 生成猫咪ID...")
    cat_ids = generate_cat_ids()
    
    for cat_name, cat_id in cat_ids.items():
        print(f"   {cat_name}: {cat_id}")
    
    # 验证图片文件存在
    print("\n✅ 验证图片文件...")
    missing_files = []
    for cat_name, images in selected_images.items():
        for img in images:
            img_path = os.path.join(image_dir, img)
            if not os.path.exists(img_path):
                missing_files.append(img_path)
    
    if missing_files:
        print("❌ 以下图片文件不存在:")
        for file in missing_files:
            print(f"   - {file}")
        return 1
    
    print("   所有图片文件都存在 ✅")
    
    # 创建配置
    print(f"\n📝 生成配置文件...")
    config = create_init_config(selected_images, cat_ids, image_dir)
    
    # 保存配置文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已保存: {output_file}")
    
    # 显示配置摘要
    print(f"\n📋 配置摘要:")
    for item in config:
        print(f"   🐱 {item['name']} (ID: {item['cat_id']})")
        print(f"      主图片: {os.path.basename(item['image_path'])}")
        if item['alternative_images']:
            print(f"      备选图片: {len(item['alternative_images'])} 张")
    
    print(f"\n🚀 使用方法:")
    print(f"   python /home/<USER>/animsi/aby/server/caby_ai/scripts/init_cats_with_images.py --config {output_file}")
    
    return 0

if __name__ == "__main__":
    exit(main())
