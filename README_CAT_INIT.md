# 基于真实图片的猫咪初始化配置

本文档说明如何使用从annotations.json和真实图片生成的配置文件来初始化三只猫的特征向量。

## 📊 数据统计

基于 `/home/<USER>/animsi/caby_training/tagging/annotations.json` 的分析结果：

- **总标注数量**: 1,518 个
- **小花**: 518 张图片
- **小黑**: 394 张图片  
- **小白**: 600 张图片

## 🎯 选中的代表性图片

为每只猫随机选择了3张图片，并使用第一张作为主要特征图片：

### 小花 (ID: f3ce1b02b5f1a8b9d31000_1752669241478093)
- **主图片**: `2025-01-28_04-13-10_hls.jpg`
- **备选图片**: 
  - `2025-05-31_22-48-31_hls.jpg`
  - `2025-06-17_19-21-26_hls.jpg`

### 小黑 (ID: f3ce1b02b2c1d755421000_1752669241478091)
- **主图片**: `2025-04-03_10-52-02_hls.jpg`
- **备选图片**:
  - `2025-03-01_13-06-19_hls.jpg`
  - `2025-06-10_01-01-40_hls.jpg`

### 小白 (ID: f3ce1b02b40e9477c21000_1752669241478092)
- **主图片**: `2025-01-27_12-32-19_hls.jpg`
- **备选图片**:
  - `2025-04-16_12-15-57_hls.jpg`
  - `2025-05-04_18-51-24_hls.jpg`

## 📁 生成的配置文件

### 1. `cats_init_config.json` (完整版)
包含主图片和备选图片的完整配置文件。

### 2. `cats_init_simple.json` (简化版)
只包含主图片的简化配置文件，适用于初始化脚本。

## 🚀 使用方法

### 方法1: 使用简化配置文件（推荐）

```bash
cd /home/<USER>/animsi/aby/server/caby_ai

# 清理现有数据（可选）
python scripts/cleanup_test_data.py

# 使用真实图片初始化
python scripts/init_cats_with_images.py --config /home/<USER>/animsi/aby/server/cats_init_simple.json

# 验证初始化结果
python scripts/test.py test_similarity --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga

# 运行完整测试
python test_shadow_complete.py
```

### 方法2: 单只猫咪初始化

如果你想单独初始化某只猫：

```bash
# 初始化小花
python scripts/init_single_cat.py \
  --cat-id f3ce1b02b5f1a8b9d31000_1752669241478093 \
  --name 小花 \
  --image "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"

# 初始化小黑
python scripts/init_single_cat.py \
  --cat-id f3ce1b02b2c1d755421000_1752669241478091 \
  --name 小黑 \
  --image "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-04-03_10-52-02_hls.jpg"

# 初始化小白
python scripts/init_single_cat.py \
  --cat-id f3ce1b02b40e9477c21000_1752669241478092 \
  --name 小白 \
  --image "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-27_12-32-19_hls.jpg"
```

## ✅ 验证步骤

1. **检查图片文件存在**:
   ```bash
   ls -la "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
   ls -la "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-04-03_10-52-02_hls.jpg"
   ls -la "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-27_12-32-19_hls.jpg"
   ```

2. **验证服务状态**:
   ```bash
   docker ps | grep caby
   ```

3. **测试相似度搜索**:
   ```bash
   python scripts/test.py test_similarity --api-key 03U66tGbSQtHGrh9IyBDjRYaSeQukFga
   ```

## 🔧 重新生成配置

如果需要重新生成配置文件（例如选择不同的图片）：

```bash
python /home/<USER>/animsi/aby/server/generate_cat_init_config.py
```

这将重新随机选择图片并生成新的配置文件。

## 📝 注意事项

1. **图片质量**: 选中的图片都是真实的猫咪照片，应该能生成高质量的特征向量
2. **ID唯一性**: 每次生成的猫咪ID都包含时间戳，确保唯一性
3. **路径正确性**: 所有图片路径都已验证存在
4. **备份**: 建议在初始化前备份现有的向量数据

## 🎉 预期结果

使用真实图片初始化后，你应该能看到：
- 更准确的猫咪识别
- 更好的相似度分数区分度
- 更稳定的影子模式性能
