-- =====================================
-- 影子模式数据库迁移脚本 (最简版)
-- =====================================
-- 
-- 执行前请确保：
-- 1. 备份数据库
-- 2. 如果遇到"字段已存在"错误，可以忽略继续执行
-- 3. 如果遇到"表已存在"错误，先手动删除表再执行
--
-- 执行方法：
-- mysql -u username -p database_name < shadow_mode_migration.sql
--

USE cats_db;

-- =====================================
-- 第一部分：为record_analysis表添加影子模式字段
-- =====================================
-- 如果字段已存在，会报错但可以忽略

ALTER TABLE record_analysis ADD COLUMN shadow_mode_result TEXT COMMENT '影子模式识别结果JSON';
ALTER TABLE record_analysis ADD COLUMN shadow_similarity DECIMAL(5,4) DEFAULT NULL COMMENT '影子模式相似度分数';
ALTER TABLE record_analysis ADD COLUMN shadow_matched_cat_id VARCHAR(32) DEFAULT NULL COMMENT '影子模式匹配的猫咪ID';
ALTER TABLE record_analysis ADD COLUMN shadow_is_new_cat BOOLEAN DEFAULT FALSE COMMENT '影子模式是否识别为新猫';
ALTER TABLE record_analysis ADD COLUMN shadow_confidence DECIMAL(5,4) DEFAULT NULL COMMENT '影子模式置信度';
ALTER TABLE record_analysis ADD COLUMN shadow_features_stored BOOLEAN DEFAULT FALSE COMMENT '特征向量是否已存储到Qdrant';
ALTER TABLE record_analysis ADD COLUMN shadow_model_version VARCHAR(32) DEFAULT NULL COMMENT '影子模式模型版本';

-- =====================================
-- 第二部分：创建用户影子模式配置表
-- =====================================

CREATE TABLE user_shadow_config (
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用影子模式',
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500 COMMENT '相似度阈值',
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000 COMMENT '新猫判断阈值',
  top_k INT DEFAULT 5 COMMENT '返回结果数量',
  notification_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用通知',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户影子模式配置表';

-- =====================================
-- 第三部分：创建影子模式通知记录表
-- =====================================

CREATE TABLE shadow_mode_notifications (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  video_id VARCHAR(64) NOT NULL COMMENT '视频ID',
  notification_type ENUM('new_cat', 'different_result', 'low_confidence') NOT NULL COMMENT '通知类型',
  message_title VARCHAR(255) NOT NULL COMMENT '通知标题',
  message_content TEXT COMMENT '通知内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='影子模式通知记录表';

-- =====================================
-- 第四部分：添加外键约束
-- =====================================

ALTER TABLE user_shadow_config 
ADD CONSTRAINT fk_user_shadow_config_user_id 
FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

ALTER TABLE shadow_mode_notifications 
ADD CONSTRAINT fk_shadow_notifications_user_id 
FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE;

-- =====================================
-- 第五部分：创建索引
-- =====================================

CREATE INDEX idx_user_shadow_config_enabled ON user_shadow_config(enabled);
CREATE INDEX idx_shadow_notifications_user_created ON shadow_mode_notifications(user_id, created_at);
CREATE INDEX idx_shadow_matched_cat ON record_analysis (shadow_matched_cat_id);
CREATE INDEX idx_shadow_is_new_cat ON record_analysis (shadow_is_new_cat);
CREATE INDEX idx_shadow_similarity ON record_analysis (shadow_similarity);

-- =====================================
-- 第六部分：插入默认配置
-- =====================================

INSERT INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold, top_k, notification_enabled)
SELECT user_id, FALSE, 0.8500, 0.7000, 5, FALSE
FROM users
WHERE status = 1;

-- =====================================
-- 第七部分：创建查询视图
-- =====================================

CREATE VIEW shadow_mode_analysis_view AS
SELECT
    ra.video_id,
    ra.animal_id,
    ra.cat_confidence,
    ra.shadow_mode_result,
    ra.shadow_similarity,
    ra.shadow_matched_cat_id,
    ra.shadow_is_new_cat,
    ra.shadow_confidence,
    ra.shadow_model_version,
    rs.device_id,
    rs.start_time,
    rs.end_time,
    d.user_id,
    c.name as original_cat_name,
    sc.name as shadow_matched_cat_name
FROM record_analysis ra
JOIN record_shit rs ON ra.video_id = rs.video_id
JOIN devices d ON rs.device_id = d.device_id
LEFT JOIN cats c ON ra.animal_id = c.cat_id
LEFT JOIN cats sc ON ra.shadow_matched_cat_id = sc.cat_id
WHERE ra.shadow_mode_result IS NOT NULL;

-- =====================================
-- 完成信息
-- =====================================

SELECT 'Shadow mode migration completed!' as status;
SELECT COUNT(*) as users_with_shadow_config FROM user_shadow_config;
