-- =====================================
-- 检查user_id字段类型一致性
-- =====================================
-- 
-- 此脚本检查所有表中user_id字段的类型是否一致
--

USE cats_db;

-- 检查所有表中的user_id字段类型
SELECT 'Checking user_id field types across all tables...' as status;

SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COLUMN_TYPE,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND COLUMN_NAME = 'user_id'
ORDER BY TABLE_NAME;

-- 检查是否有不一致的user_id字段类型
SELECT 'Checking for inconsistent user_id field types...' as status;

SELECT 
    COLUMN_TYPE,
    COUNT(*) as table_count,
    GROUP_CONCAT(TABLE_NAME) as tables
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND COLUMN_NAME = 'user_id'
GROUP BY COLUMN_TYPE
ORDER BY table_count DESC;

-- 验证外键约束
SELECT 'Checking foreign key constraints for user_id...' as status;

SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND COLUMN_NAME = 'user_id'
    AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME;

-- 显示检查完成信息
SELECT 'User ID field type consistency check completed!' as status;
