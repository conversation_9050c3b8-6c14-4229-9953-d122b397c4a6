-- =====================================
-- 影子模式数据库迁移脚本 (最终版)
-- =====================================
-- 
-- 此脚本安全地添加影子模式相关的数据库结构
-- 处理字段和表已存在的情况
--
-- 执行方法：
-- mysql -u username -p database_name < add_shadow_mode_final.sql
--

USE cats_db;

-- 检查并添加record_analysis表的影子模式字段
-- 只有当字段不存在时才添加

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_mode_result') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_mode_result TEXT COMMENT ''影子模式识别结果JSON''',
    'SELECT ''shadow_mode_result column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_similarity') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_similarity DECIMAL(5,4) DEFAULT NULL COMMENT ''影子模式相似度分数''',
    'SELECT ''shadow_similarity column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_matched_cat_id') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_matched_cat_id VARCHAR(32) DEFAULT NULL COMMENT ''影子模式匹配的猫咪ID''',
    'SELECT ''shadow_matched_cat_id column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_is_new_cat') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_is_new_cat BOOLEAN DEFAULT FALSE COMMENT ''影子模式是否识别为新猫''',
    'SELECT ''shadow_is_new_cat column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_confidence') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_confidence DECIMAL(5,4) DEFAULT NULL COMMENT ''影子模式置信度''',
    'SELECT ''shadow_confidence column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_features_stored') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_features_stored BOOLEAN DEFAULT FALSE COMMENT ''特征向量是否已存储到Qdrant''',
    'SELECT ''shadow_features_stored column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME='record_analysis' AND COLUMN_NAME='shadow_model_version') = 0,
    'ALTER TABLE record_analysis ADD COLUMN shadow_model_version VARCHAR(32) DEFAULT NULL COMMENT ''影子模式模型版本''',
    'SELECT ''shadow_model_version column already exists'' as status'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 删除可能存在的旧表
DROP TABLE IF EXISTS user_shadow_config;
DROP TABLE IF EXISTS shadow_mode_notifications;

-- 创建用户影子模式配置表
CREATE TABLE user_shadow_config (
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用影子模式',
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500 COMMENT '相似度阈值',
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000 COMMENT '新猫判断阈值',
  top_k INT DEFAULT 5 COMMENT '返回结果数量',
  notification_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用通知',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (user_id),
  CONSTRAINT fk_user_shadow_config_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户影子模式配置表';

-- 创建影子模式通知记录表
CREATE TABLE shadow_mode_notifications (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  video_id VARCHAR(64) NOT NULL COMMENT '视频ID',
  notification_type ENUM('new_cat', 'different_result', 'low_confidence') NOT NULL COMMENT '通知类型',
  message_title VARCHAR(255) NOT NULL COMMENT '通知标题',
  message_content TEXT COMMENT '通知内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_created (user_id, created_at),
  CONSTRAINT fk_shadow_notifications_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='影子模式通知记录表';

-- 创建索引（安全地）
CREATE INDEX idx_user_shadow_config_enabled ON user_shadow_config(enabled);

-- 为record_analysis表创建索引（忽略已存在的错误）
SET @sql = 'CREATE INDEX idx_shadow_matched_cat ON record_analysis (shadow_matched_cat_id)';
SET @ignore_error = 0;
DECLARE CONTINUE HANDLER FOR 1061 SET @ignore_error = 1;
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_shadow_is_new_cat ON record_analysis (shadow_is_new_cat)';
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_shadow_similarity ON record_analysis (shadow_similarity)';
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 插入默认配置
INSERT IGNORE INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold, top_k, notification_enabled)
SELECT user_id, FALSE, 0.8500, 0.7000, 5, FALSE
FROM users
WHERE status = 1;

-- 创建视图
CREATE OR REPLACE VIEW shadow_mode_analysis_view AS
SELECT
    ra.video_id,
    ra.animal_id,
    ra.cat_confidence,
    ra.shadow_mode_result,
    ra.shadow_similarity,
    ra.shadow_matched_cat_id,
    ra.shadow_is_new_cat,
    ra.shadow_confidence,
    ra.shadow_model_version,
    rs.device_id,
    rs.start_time,
    rs.end_time,
    d.user_id,
    c.name as original_cat_name,
    sc.name as shadow_matched_cat_name
FROM record_analysis ra
JOIN record_shit rs ON ra.video_id = rs.video_id
JOIN devices d ON rs.device_id = d.device_id
LEFT JOIN cats c ON ra.animal_id = c.cat_id
LEFT JOIN cats sc ON ra.shadow_matched_cat_id = sc.cat_id
WHERE ra.shadow_mode_result IS NOT NULL;

-- 显示完成信息
SELECT 'Shadow mode database migration completed successfully!' as status;
