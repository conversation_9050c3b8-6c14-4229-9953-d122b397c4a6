# Backend Server 影子模式数据库迁移指南

## 📋 概述

本文档说明了为Backend Server影子模式功能所需的数据库修改。影子模式已完全集成到后台视频分析流程中，支持用户级别的配置管理。

## 🗃️ 数据库修改内容

### 1. record_analysis表 - 添加影子模式字段

为现有的`record_analysis`表添加以下字段来存储影子模式识别结果：

```sql
-- 影子模式识别结果JSON
shadow_mode_result TEXT

-- 影子模式相似度分数 (0.0000-1.0000)
shadow_similarity DECIMAL(5,4) DEFAULT NULL

-- 影子模式匹配的猫咪ID
shadow_matched_cat_id VARCHAR(32) DEFAULT NULL

-- 影子模式是否识别为新猫
shadow_is_new_cat BOOLEAN DEFAULT FALSE

-- 影子模式置信度 (0.0000-1.0000)
shadow_confidence DECIMAL(5,4) DEFAULT NULL

-- 特征向量是否已存储到Qdrant
shadow_features_stored BOOLEAN DEFAULT FALSE

-- 影子模式模型版本
shadow_model_version VARCHAR(32) DEFAULT NULL
```

### 2. user_shadow_config表 - 用户影子模式配置

创建新表来存储每个用户的影子模式配置：

```sql
CREATE TABLE user_shadow_config (
  user_id VARCHAR(255) PRIMARY KEY,
  enabled BOOLEAN DEFAULT FALSE,
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500,
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000,
  top_k INT DEFAULT 5,
  notification_enabled BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. shadow_mode_notifications表 - 影子模式通知记录

创建表来记录影子模式触发的通知：

```sql
CREATE TABLE shadow_mode_notifications (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL,
  video_id VARCHAR(64) NOT NULL,
  notification_type ENUM('new_cat', 'different_result', 'low_confidence') NOT NULL,
  message_title VARCHAR(255) NOT NULL,
  message_content TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🚀 执行迁移

### 步骤1：备份数据库
```bash
mysqldump -u username -p cats_db > cats_db_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 步骤2：执行主迁移脚本
```bash
mysql -u username -p cats_db < database/migrations/add_shadow_mode_columns.sql
```

### 步骤3：验证迁移结果
```bash
mysql -u username -p cats_db < database/migrations/verify_shadow_mode_migration.sql
```

## 📊 预期结果

迁移完成后，你应该看到：

1. **record_analysis表**：新增7个影子模式相关字段
2. **user_shadow_config表**：新创建，包含8个字段
3. **shadow_mode_notifications表**：新创建，包含6个字段
4. **索引**：为性能优化添加的相关索引
5. **视图**：`shadow_mode_analysis_view`用于便捷查询
6. **默认数据**：为现有用户创建默认配置（禁用状态）

## ⚙️ 配置说明

### 用户影子模式配置参数

- **enabled**: 是否启用影子模式（默认：false）
- **similarity_threshold**: 相似度阈值，高于此值认为是已知猫咪（默认：0.85）
- **new_cat_threshold**: 新猫阈值，低于此值认为是新猫（默认：0.70）
- **top_k**: 返回的相似结果数量（默认：5）
- **notification_enabled**: 是否启用影子模式通知（默认：false）

### 影子模式工作流程

1. **视频分析触发**：用户上传视频进行分析
2. **用户配置检查**：系统检查用户是否启用影子模式
3. **异步处理**：如果启用，在后台异步进行影子模式识别
4. **结果存储**：识别结果自动保存到`record_analysis`表
5. **通知处理**：根据配置发送相关通知

## 🔧 管理操作

### 为用户启用影子模式
```sql
INSERT INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold) 
VALUES ('user_123', TRUE, 0.85, 0.70)
ON DUPLICATE KEY UPDATE enabled = TRUE;
```

### 查看影子模式结果
```sql
SELECT * FROM shadow_mode_analysis_view 
WHERE user_id = 'user_123' 
ORDER BY created_at DESC 
LIMIT 10;
```

### 禁用用户影子模式
```sql
UPDATE user_shadow_config 
SET enabled = FALSE 
WHERE user_id = 'user_123';
```

## ⚠️ 注意事项

1. **默认状态**：所有用户的影子模式默认为禁用状态
2. **性能影响**：影子模式使用异步处理，不影响主要业务流程
3. **存储空间**：影子模式结果会增加数据库存储需求
4. **API变更**：影子模式完全在后台运行，不暴露API端点
5. **配置管理**：需要通过数据库直接操作来管理用户配置

## 🎯 后续步骤

迁移完成后：

1. **测试验证**：使用测试脚本验证影子模式功能
2. **用户配置**：为需要的用户启用影子模式
3. **监控观察**：观察影子模式的识别效果和性能影响
4. **参数调优**：根据实际效果调整相似度阈值等参数

---

**影子模式现在已完全集成到Backend Server中，可以为用户提供更准确的个性化猫咪识别服务！** 🎉
