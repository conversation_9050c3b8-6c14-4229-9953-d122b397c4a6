-- =====================================
-- 影子模式数据库迁移脚本 (修复版)
-- =====================================
-- 
-- 此脚本为record_analysis表添加影子模式相关列
-- 用于存储影子模式识别结果和比较数据
-- 修复了字符集和排序规则兼容性问题
--
-- 执行方法：
-- mysql -u username -p database_name < add_shadow_mode_columns_fixed.sql
--

USE cats_db;

-- 为record_analysis表添加影子模式相关列
ALTER TABLE record_analysis 
ADD COLUMN IF NOT EXISTS shadow_mode_result TEXT COMMENT '影子模式识别结果JSON',
ADD COLUMN IF NOT EXISTS shadow_similarity DECIMAL(5,4) DEFAULT NULL COMMENT '影子模式相似度分数',
ADD COLUMN IF NOT EXISTS shadow_matched_cat_id VARCHAR(32) DEFAULT NULL COMMENT '影子模式匹配的猫咪ID',
ADD COLUMN IF NOT EXISTS shadow_is_new_cat BOOLEAN DEFAULT FALSE COMMENT '影子模式是否识别为新猫',
ADD COLUMN IF NOT EXISTS shadow_confidence DECIMAL(5,4) DEFAULT NULL COMMENT '影子模式置信度',
ADD COLUMN IF NOT EXISTS shadow_features_stored BOOLEAN DEFAULT FALSE COMMENT '特征向量是否已存储到Qdrant',
ADD COLUMN IF NOT EXISTS shadow_model_version VARCHAR(32) DEFAULT NULL COMMENT '影子模式模型版本';

-- 删除可能存在的旧表（如果存在）
DROP TABLE IF EXISTS user_shadow_config;
DROP TABLE IF EXISTS shadow_mode_notifications;

-- 创建用户影子模式配置表 (确保字符集与users表完全匹配)
CREATE TABLE user_shadow_config (
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用影子模式',
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500 COMMENT '相似度阈值',
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000 COMMENT '新猫判断阈值',
  top_k INT DEFAULT 5 COMMENT '返回结果数量',
  notification_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用通知',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (user_id),
  CONSTRAINT fk_user_shadow_config_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户影子模式配置表';

-- 为用户影子模式配置表添加索引
CREATE INDEX idx_user_shadow_config_enabled ON user_shadow_config(enabled);

-- 创建影子模式通知记录表 (确保字符集与users表完全匹配)
CREATE TABLE shadow_mode_notifications (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
  video_id VARCHAR(64) NOT NULL COMMENT '视频ID',
  notification_type ENUM('new_cat', 'different_result', 'low_confidence') NOT NULL COMMENT '通知类型',
  message_title VARCHAR(255) NOT NULL COMMENT '通知标题',
  message_content TEXT COMMENT '通知内容',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_created (user_id, created_at),
  CONSTRAINT fk_shadow_notifications_user_id FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='影子模式通知记录表';

-- 为新增列添加索引以提高查询性能
-- 安全地创建索引，如果索引已存在则忽略错误

-- 创建临时存储过程来安全地添加索引
DELIMITER $$

CREATE PROCEDURE AddIndexIfNotExists(
    IN table_name VARCHAR(128),
    IN index_name VARCHAR(128),
    IN column_name VARCHAR(128)
)
BEGIN
    DECLARE index_exists INT DEFAULT 0;

    SELECT COUNT(*) INTO index_exists
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = table_name
        AND INDEX_NAME = index_name;

    IF index_exists = 0 THEN
        SET @sql = CONCAT('CREATE INDEX ', index_name, ' ON ', table_name, ' (', column_name, ')');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
    END IF;
END$$

DELIMITER ;

-- 使用存储过程安全地创建索引
CALL AddIndexIfNotExists('record_analysis', 'idx_shadow_matched_cat', 'shadow_matched_cat_id');
CALL AddIndexIfNotExists('record_analysis', 'idx_shadow_is_new_cat', 'shadow_is_new_cat');
CALL AddIndexIfNotExists('record_analysis', 'idx_shadow_similarity', 'shadow_similarity');

-- 删除临时存储过程
DROP PROCEDURE AddIndexIfNotExists;

-- 插入默认的影子模式配置（为现有用户，默认禁用）
INSERT IGNORE INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold, top_k, notification_enabled)
SELECT user_id, FALSE, 0.8500, 0.7000, 5, FALSE
FROM users
WHERE status = 1;

-- 创建视图以便于查询影子模式结果
CREATE OR REPLACE VIEW shadow_mode_analysis_view AS
SELECT
    ra.video_id,
    ra.animal_id,
    ra.cat_confidence,
    ra.shadow_mode_result,
    ra.shadow_similarity,
    ra.shadow_matched_cat_id,
    ra.shadow_is_new_cat,
    ra.shadow_confidence,
    ra.shadow_model_version,
    rs.device_id,
    rs.start_time,
    rs.end_time,
    d.user_id,
    c.name as original_cat_name,
    sc.name as shadow_matched_cat_name
FROM record_analysis ra
JOIN record_shit rs ON ra.video_id = rs.video_id
JOIN devices d ON rs.device_id = d.device_id
LEFT JOIN cats c ON ra.animal_id = c.cat_id
LEFT JOIN cats sc ON ra.shadow_matched_cat_id = sc.cat_id
WHERE ra.shadow_mode_result IS NOT NULL;

-- 添加注释说明
ALTER TABLE record_analysis COMMENT = '视频分析结果表 - 包含原始识别和影子模式识别结果';
ALTER TABLE user_shadow_config COMMENT = '用户影子模式配置表 - 使用user_id作为主键，每用户一条配置';
ALTER TABLE shadow_mode_notifications COMMENT = '影子模式通知记录表 - 记录影子模式触发的通知';

-- 验证外键约束
SELECT 'Verifying foreign key constraints...' as status;
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND TABLE_NAME IN ('user_shadow_config', 'shadow_mode_notifications')
    AND REFERENCED_TABLE_NAME IS NOT NULL;

-- 显示迁移完成信息
SELECT 'Shadow mode database migration completed successfully!' as status;
SELECT 'Tables created with proper charset and collation compatibility!' as status;
