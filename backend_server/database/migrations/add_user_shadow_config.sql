-- =====================================
-- 用户影子模式配置表迁移脚本
-- =====================================
-- 
-- 此脚本创建用户级别的影子模式配置表
-- 用于存储每个用户的影子模式设置
--
-- 执行方法：
-- mysql -u username -p database_name < add_user_shadow_config.sql
--

USE cabycare;

-- 创建用户影子模式配置表
CREATE TABLE IF NOT EXISTS user_shadow_config (
  user_id VARCHAR(255) PRIMARY KEY COMMENT '用户ID',
  enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用影子模式',
  similarity_threshold DECIMAL(5,4) DEFAULT 0.8500 COMMENT '相似度阈值',
  new_cat_threshold DECIMAL(5,4) DEFAULT 0.7000 COMMENT '新猫判断阈值',
  top_k INT DEFAULT 5 COMMENT '返回结果数量',
  notification_enabled BOOLEAN DEFAULT FALSE COMMENT '是否启用通知',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户影子模式配置表';

-- 为用户ID添加索引
CREATE INDEX idx_user_shadow_config_user_id ON user_shadow_config(user_id);

-- 为启用状态添加索引
CREATE INDEX idx_user_shadow_config_enabled ON user_shadow_config(enabled);

-- 显示迁移完成信息
SELECT 'User shadow mode configuration table created successfully!' as status;
