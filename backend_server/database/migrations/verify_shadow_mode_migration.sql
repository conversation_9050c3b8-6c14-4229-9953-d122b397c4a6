-- =====================================
-- 影子模式数据库迁移验证脚本
-- =====================================
-- 
-- 此脚本验证影子模式相关的数据库表和字段是否正确创建
--
-- 执行方法：
-- mysql -u username -p database_name < verify_shadow_mode_migration.sql
--

USE cats_db;

-- 验证record_analysis表的影子模式字段
SELECT 'Checking record_analysis shadow mode columns...' as status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND TABLE_NAME = 'record_analysis' 
    AND COLUMN_NAME LIKE 'shadow_%'
ORDER BY ORDINAL_POSITION;

-- 验证user_shadow_config表
SELECT 'Checking user_shadow_config table...' as status;

SELECT
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'cats_db'
    AND TABLE_NAME = 'user_shadow_config'
ORDER BY ORDINAL_POSITION;

-- 特别验证user_id字段类型
SELECT 'Verifying user_id field type...' as status;
SELECT
    COLUMN_NAME,
    COLUMN_TYPE,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'cats_db'
    AND TABLE_NAME = 'user_shadow_config'
    AND COLUMN_NAME = 'user_id';

-- 验证shadow_mode_notifications表
SELECT 'Checking shadow_mode_notifications table...' as status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND TABLE_NAME = 'shadow_mode_notifications'
ORDER BY ORDINAL_POSITION;

-- 验证索引
SELECT 'Checking indexes...' as status;

SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND (
        (TABLE_NAME = 'record_analysis' AND INDEX_NAME LIKE 'idx_shadow_%') OR
        (TABLE_NAME = 'user_shadow_config' AND INDEX_NAME LIKE 'idx_%') OR
        (TABLE_NAME = 'shadow_mode_notifications' AND INDEX_NAME LIKE 'idx_%')
    )
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 验证视图
SELECT 'Checking shadow_mode_analysis_view...' as status;

SELECT 
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'cats_db' 
    AND TABLE_NAME = 'shadow_mode_analysis_view'
ORDER BY ORDINAL_POSITION;

-- 统计现有数据
SELECT 'Data statistics...' as status;

SELECT 
    'record_analysis' as table_name,
    COUNT(*) as total_records,
    COUNT(shadow_mode_result) as records_with_shadow_result
FROM record_analysis
UNION ALL
SELECT 
    'user_shadow_config' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_users
FROM user_shadow_config
UNION ALL
SELECT 
    'shadow_mode_notifications' as table_name,
    COUNT(*) as total_records,
    0 as placeholder
FROM shadow_mode_notifications;

-- 显示验证完成信息
SELECT 'Shadow mode database migration verification completed!' as status;
