CREATE DATABASE `cat_toilet_db` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
-- cat_toilet_db.users definition
-- cats_db.ota_download_cache definition

CREATE TABLE `ota_download_cache` (
  `id` int NOT NULL AUTO_INCREMENT,
  `version` varchar(32) NOT NULL COMMENT 'OTA版本号',
  `download_url` text NOT NULL COMMENT '下载链接',
  `generated_at` timestamp NOT NULL COMMENT '生成时间',
  `expires_at` timestamp NOT NULL COMMENT '过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `md5_url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `version` (`version`),
  UNIQUE KEY `idx_version` (`version`),
  KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='OTA下载链接缓存表';


-- cats_db.pending_notifications definition

CREATE TABLE `pending_notifications` (
  `video_id` varchar(64) NOT NULL,
  `type` varchar(32) NOT NULL,
  `subtype` varchar(32) DEFAULT NULL,
  `user_id` varchar(20) NOT NULL,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `priority` tinyint NOT NULL DEFAULT '0',
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`video_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.users definition

CREATE TABLE `users` (
  `user_id` varchar(20) NOT NULL,
  `logto_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'Logto 用户 ID',
  `username` varchar(64) NOT NULL,
  `password_hash` varchar(64) NOT NULL,
  `email` varchar(128) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `nickname` varchar(64) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cats definition

CREATE TABLE `cats` (
  `cat_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `name` varchar(64) NOT NULL,
  `birthday` date DEFAULT NULL,
  `gender` tinyint DEFAULT '0',
  `breed` varchar(32) DEFAULT NULL,
  `color` varchar(32) DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `weight` double DEFAULT NULL,
  PRIMARY KEY (`cat_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `cats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.clients definition

CREATE TABLE `clients` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `client_id` varchar(64) NOT NULL,
  `client_type` varchar(32) NOT NULL,
  `name` varchar(64) DEFAULT NULL,
  `model` varchar(32) DEFAULT NULL,
  `os_version` varchar(32) DEFAULT NULL,
  `app_version` varchar(32) DEFAULT NULL,
  `last_active` timestamp NULL DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_id` (`client_id`),
  KEY `idx_user_client` (`user_id`,`client_id`),
  CONSTRAINT `clients_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.clients_tokens definition

CREATE TABLE `clients_tokens` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `client_id` varchar(64) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `client_token` varchar(255) NOT NULL,
  `token_type` varchar(32) DEFAULT 'apns',
  `is_sandbox` tinyint(1) DEFAULT '0',
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_client_token` (`client_token`),
  KEY `clients_tokens_ibfk_2` (`user_id`),
  KEY `idx_client_user` (`client_id`,`user_id`),
  CONSTRAINT `clients_tokens_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  CONSTRAINT `clients_tokens_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=135 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.devices definition

CREATE TABLE `devices` (
  `device_id` varchar(32) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `model` varchar(32) DEFAULT NULL,
  `timezone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `hardware_sn` varchar(64) NOT NULL,
  `firmware_version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `last_heartbeat` timestamp NULL DEFAULT NULL,
  `last_active` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`device_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `devices_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.family_groups definition

CREATE TABLE `family_groups` (
  `group_id` varchar(32) NOT NULL,
  `group_name` varchar(64) NOT NULL,
  `owner_id` varchar(20) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `max_members` int NOT NULL DEFAULT '8',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`group_id`),
  KEY `family_groups_ibfk_1` (`owner_id`),
  CONSTRAINT `family_groups_ibfk_1` FOREIGN KEY (`owner_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.notification_settings definition

CREATE TABLE `notification_settings` (
  `user_id` varchar(20) NOT NULL,
  `enable_daily` tinyint(1) NOT NULL DEFAULT '1',
  `enable_stats` tinyint(1) NOT NULL DEFAULT '1',
  `quiet_hours_start` int DEFAULT NULL COMMENT '免打扰开始时间(分钟数，0-1439)',
  `quiet_hours_end` int DEFAULT NULL COMMENT '免打扰结束时间(分钟数，0-1439)',
  `timezone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'Asia/Hong_Kong',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `notification_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.notifications definition

CREATE TABLE `notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `notice_id` longtext,
  `type` varchar(32) NOT NULL,
  `subtype` varchar(32) DEFAULT NULL,
  `user_id` varchar(20) NOT NULL,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `priority` tinyint NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `metadata` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_ibfk_1` (`user_id`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=1082 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.quiet_notifications definition

CREATE TABLE `quiet_notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `cat_id` varchar(20) NOT NULL,
  `type` varchar(32) NOT NULL,
  `subtype` varchar(32) NOT NULL,
  `title` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `duration` int NOT NULL COMMENT '使用秒数',
  `count` int NOT NULL DEFAULT '0' COMMENT '通知次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `quiet_notifications_ibfk_2` (`cat_id`),
  KEY `idx_user_cat` (`user_id`,`cat_id`),
  KEY `idx_type_subtype` (`type`,`subtype`),
  CONSTRAINT `quiet_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  CONSTRAINT `quiet_notifications_ibfk_2` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=186 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.record_shit definition

CREATE TABLE `record_shit` (
  `video_id` varchar(64) NOT NULL,
  `device_id` varchar(64) NOT NULL,
  `start_time` bigint NOT NULL,
  `end_time` bigint DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `process_stage` tinyint NOT NULL DEFAULT '0',
  `weight_litter` float NOT NULL,
  `weight_cat` float NOT NULL,
  `weight_waste` float NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`video_id`),
  KEY `record_shit_ibfk_1` (`device_id`),
  CONSTRAINT `record_shit_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.user_hardware definition

CREATE TABLE `user_hardware` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL,
  `hardware_sn` varchar(32) NOT NULL,
  `status` tinyint DEFAULT '1',
  `remark` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_hardware_sn` (`hardware_sn`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `user_hardware_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=27296 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.user_relations definition

CREATE TABLE `user_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) DEFAULT NULL,
  `related_id` varchar(20) DEFAULT NULL,
  `type` tinyint DEFAULT NULL,
  `role` tinyint DEFAULT NULL,
  `permissions` text,
  `status` tinyint DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_users` (`user_id`,`related_id`),
  KEY `related_id` (`related_id`),
  CONSTRAINT `user_relations_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `user_relations_ibfk_2` FOREIGN KEY (`related_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.user_settings definition

CREATE TABLE `user_settings` (
  `user_id` varchar(20) NOT NULL,
  `language` varchar(8) DEFAULT 'zh',
  `time_zone` varchar(32) DEFAULT NULL,
  `notification` tinyint(1) DEFAULT '1',
  `newsletter` tinyint(1) DEFAULT '1',
  `theme` varchar(16) DEFAULT NULL,
  `settings` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cat_alerts definition

CREATE TABLE `cat_alerts` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` varchar(20) DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `level` tinyint DEFAULT '1',
  `title` varchar(128) DEFAULT NULL,
  `description` text,
  `suggestions` text,
  `status` tinyint DEFAULT '1',
  `process_time` timestamp NULL DEFAULT NULL,
  `process_note` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_id` (`cat_id`),
  CONSTRAINT `cat_alerts_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cat_behaviors definition

CREATE TABLE `cat_behaviors` (
  `cat_id` varchar(20) NOT NULL,
  `toilet_frequency` int DEFAULT NULL,
  `diet_preference` text,
  `activity_level` tinyint DEFAULT '2',
  `indoor_only` tinyint(1) DEFAULT '1',
  `facial_features` text,
  `body_features` text,
  `behavior_tags` text,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`cat_id`),
  CONSTRAINT `cat_behaviors_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cat_health definition

CREATE TABLE `cat_health` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` varchar(20) DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `weight` decimal(5,2) DEFAULT NULL,
  `temperature` decimal(3,1) DEFAULT NULL,
  `description` text,
  `attachments` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_id` (`cat_id`),
  CONSTRAINT `cat_health_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cat_metrics_daily definition

CREATE TABLE `cat_metrics_daily` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` varchar(20) NOT NULL,
  `metric_date` date NOT NULL,
  `weight` decimal(5,2) DEFAULT NULL,
  `weight_min` decimal(5,2) DEFAULT NULL,
  `weight_max` decimal(5,2) DEFAULT NULL,
  `weight_variance` decimal(5,3) DEFAULT NULL,
  `toilet_count` int DEFAULT '0',
  `urine_count` int DEFAULT '0',
  `stool_count` int DEFAULT '0',
  `abnormal_count` int DEFAULT '0',
  `total_duration` int DEFAULT '0',
  `avg_duration` int DEFAULT '0',
  `total_waste_weight` decimal(6,3) DEFAULT NULL,
  `avg_waste_weight` decimal(5,3) DEFAULT NULL,
  `morning_count` int DEFAULT NULL,
  `afternoon_count` int DEFAULT NULL,
  `evening_count` int DEFAULT NULL,
  `night_count` int DEFAULT NULL,
  `health_score` decimal(4,1) DEFAULT NULL,
  `hydration_level` decimal(3,2) DEFAULT NULL,
  `digestion_score` decimal(3,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_date` (`cat_id`,`metric_date`),
  CONSTRAINT `cat_metrics_daily_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cat_metrics_monthly definition

CREATE TABLE `cat_metrics_monthly` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` varchar(20) NOT NULL,
  `year` int NOT NULL,
  `month` int NOT NULL,
  `avg_weight` decimal(5,2) DEFAULT NULL,
  `weight_trend` decimal(4,2) DEFAULT NULL,
  `total_toilet_count` int DEFAULT NULL,
  `avg_toilet_count` decimal(4,1) DEFAULT NULL,
  `total_abnormal` int DEFAULT NULL,
  `health_score` decimal(4,1) DEFAULT NULL,
  `health_trend` decimal(4,1) DEFAULT NULL,
  `suggestions` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cat_month` (`cat_id`,`year`,`month`),
  CONSTRAINT `cat_metrics_monthly_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.cat_relations definition

CREATE TABLE `cat_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cat_id` varchar(20) DEFAULT NULL,
  `related_id` varchar(20) DEFAULT NULL,
  `type` tinyint DEFAULT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_cats` (`cat_id`,`related_id`),
  KEY `related_id` (`related_id`),
  CONSTRAINT `cat_relations_ibfk_1` FOREIGN KEY (`cat_id`) REFERENCES `cats` (`cat_id`),
  CONSTRAINT `cat_relations_ibfk_2` FOREIGN KEY (`related_id`) REFERENCES `cats` (`cat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_all_settings definition

CREATE TABLE `device_all_settings` (
  `device_id` varchar(64) NOT NULL,
  `auto_ota_upgrade` varchar(32) DEFAULT 'off',
  `idle_update_start_hour` TINYINT DEFAULT 2 COMMENT '闲时更新开始时间（小时，0-23）',
  `idle_update_end_hour` TINYINT DEFAULT 4 COMMENT '闲时更新结束时间（小时，0-23）',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_all_settings_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_configs definition

CREATE TABLE `device_configs` (
  `device_id` varchar(32) NOT NULL,
  `record_mode` tinyint DEFAULT '1',
  `resolution` varchar(16) DEFAULT NULL,
  `framerate` int DEFAULT NULL,
  `bitrate` int DEFAULT NULL,
  `audio_enabled` tinyint(1) DEFAULT '0',
  `motion_detection` tinyint(1) DEFAULT '1',
  `motion_sensitivity` int DEFAULT NULL,
  `storage_days` int DEFAULT '7',
  `night_vision` tinyint DEFAULT '1',
  `config_version` int DEFAULT '1',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_configs_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_logs definition

CREATE TABLE `device_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(32) DEFAULT NULL,
  `type` varchar(32) DEFAULT NULL,
  `level` tinyint DEFAULT '1',
  `content` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  CONSTRAINT `device_logs_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_ota_status definition

CREATE TABLE `device_ota_status` (
  `device_id` varchar(64) NOT NULL,
  `status` varchar(32) NOT NULL DEFAULT 'idle' COMMENT 'OTA状态: idle-空闲, updating-更新中, failed-失败, completed-完成',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_ota_status_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_status definition

CREATE TABLE `device_status` (
  `device_id` varchar(64) NOT NULL,
  `online` tinyint DEFAULT '0',
  `ipv4` varchar(39) DEFAULT NULL,
  `ipv6` varchar(39) DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_status_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_status_histories definition

CREATE TABLE `device_status_histories` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `device_id` varchar(64) NOT NULL,
  `online` tinyint DEFAULT '0',
  `signal_strength` int DEFAULT '0',
  `storage_usage` int DEFAULT '0',
  `ipv4` varchar(15) DEFAULT NULL,
  `ipv6` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_time` (`device_id`,`created_at`),
  CONSTRAINT `device_status_histories_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1432 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.device_status_statistics definition

CREATE TABLE `device_status_statistics` (
  `device_id` varchar(64) NOT NULL,
  `online_rate` int DEFAULT '0',
  `power_supply_rate` int DEFAULT '0',
  `signal_strength_avg` int DEFAULT '0',
  `storage_usage` int DEFAULT '0',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  CONSTRAINT `device_status_statistics_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.family_group_devices definition

CREATE TABLE `family_group_devices` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) NOT NULL,
  `device_id` varchar(64) NOT NULL,
  `added_by` varchar(20) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_group_device` (`group_id`,`device_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `family_group_devices_ibfk_3` (`added_by`),
  CONSTRAINT `family_group_devices_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `family_groups` (`group_id`),
  CONSTRAINT `family_group_devices_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `devices` (`device_id`),
  CONSTRAINT `family_group_devices_ibfk_3` FOREIGN KEY (`added_by`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.family_group_invitations definition

CREATE TABLE `family_group_invitations` (
  `invitation_id` varchar(32) NOT NULL,
  `group_id` varchar(32) NOT NULL,
  `inviter_id` varchar(20) NOT NULL,
  `invitee_id` varchar(20) NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0-待处理, 1-已接受, 2-已拒绝, 3-已过期',
  `role` tinyint NOT NULL DEFAULT '0' COMMENT '0-普通成员, 1-管理员',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expire_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`invitation_id`),
  UNIQUE KEY `idx_group_invitee` (`group_id`,`invitee_id`,`status`),
  KEY `idx_invitee_id` (`invitee_id`),
  KEY `idx_inviter_id` (`inviter_id`),
  CONSTRAINT `family_group_invitations_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `family_groups` (`group_id`),
  CONSTRAINT `family_group_invitations_ibfk_2` FOREIGN KEY (`inviter_id`) REFERENCES `users` (`user_id`),
  CONSTRAINT `family_group_invitations_ibfk_3` FOREIGN KEY (`invitee_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.family_group_members definition

CREATE TABLE `family_group_members` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` varchar(32) NOT NULL,
  `user_id` varchar(20) NOT NULL,
  `nickname` varchar(64) DEFAULT NULL,
  `role` tinyint NOT NULL DEFAULT '0' COMMENT '0-普通成员, 1-管理员, 2-拥有者',
  `join_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_group_user` (`group_id`,`user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `family_group_members_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `family_groups` (`group_id`),
  CONSTRAINT `family_group_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- cats_db.record_analysis definition

CREATE TABLE `record_analysis` (
  `video_id` varchar(64) NOT NULL,
  `animal_id` varchar(20) NOT NULL,
  `cat_confidence` decimal(5,4) DEFAULT '0.0000',
  `behavior_type` varchar(32) NOT NULL,
  `is_abnormal` tinyint(1) NOT NULL DEFAULT '0',
  `abnormal_type` varchar(32) NOT NULL,
  `abnormal_prob` decimal(5,4) DEFAULT '0.0000',
  `shadow_mode_result` text COMMENT '影子模式识别结果JSON',
  `shadow_similarity` decimal(5,4) DEFAULT NULL COMMENT '影子模式相似度分数',
  `shadow_matched_cat_id` varchar(32) DEFAULT NULL COMMENT '影子模式匹配的猫咪ID',
  `shadow_is_new_cat` tinyint(1) DEFAULT '0' COMMENT '影子模式是否识别为新猫',
  `shadow_confidence` decimal(5,4) DEFAULT NULL COMMENT '影子模式置信度',
  `shadow_features_stored` tinyint(1) DEFAULT '0' COMMENT '特征向量是否已存储到Qdrant',
  `shadow_model_version` varchar(32) DEFAULT NULL COMMENT '影子模式模型版本',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`video_id`),
  KEY `idx_shadow_matched_cat` (`shadow_matched_cat_id`),
  KEY `idx_shadow_is_new_cat` (`shadow_is_new_cat`),
  KEY `idx_shadow_similarity` (`shadow_similarity`),
  CONSTRAINT `record_analysis_ibfk_1` FOREIGN KEY (`video_id`) REFERENCES `record_shit` (`video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='视频分析结果表 - 包含原始识别和影子模式识别结果';


-- cats_db.device_sensor_status definition

CREATE TABLE `device_sensor_status` (
  `device_id` varchar(255) NOT NULL,
  `camera_last_error_time` datetime DEFAULT NULL COMMENT '相机最后错误时间',
  `camera_last_error_type` varchar(100) DEFAULT NULL COMMENT '相机最后错误类型',
  `weight_sensor_last_error_time` datetime DEFAULT NULL COMMENT '重量传感器最后错误时间',
  `weight_sensor_last_error_type` varchar(100) DEFAULT NULL COMMENT '重量传感器最后错误类型',
  `temperature_humidity_sensor_last_error_time` datetime DEFAULT NULL COMMENT '温湿度传感器最后错误时间',
  `temperature_humidity_sensor_last_error_type` varchar(100) DEFAULT NULL COMMENT '温湿度传感器最后错误类型',
  `microphone_last_error_time` datetime DEFAULT NULL COMMENT '麦克风最后错误时间',
  `microphone_last_error_type` varchar(100) DEFAULT NULL COMMENT '麦克风最后错误类型',
  `wifi_last_error_time` datetime DEFAULT NULL COMMENT 'WiFi最后错误时间',
  `wifi_last_error_type` varchar(100) DEFAULT NULL COMMENT 'WiFi最后错误类型',
  `bluetooth_last_error_time` datetime DEFAULT NULL COMMENT '蓝牙最后错误时间',
  `bluetooth_last_error_type` varchar(100) DEFAULT NULL COMMENT '蓝牙最后错误类型',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`device_id`),
  KEY `idx_device_sensor_status_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备传感器异常状态记录表';


-- cats_db.user_shadow_config definition

CREATE TABLE `user_shadow_config` (
  `user_id` varchar(20) NOT NULL COMMENT '用户ID',
  `enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用影子模式',
  `similarity_threshold` decimal(5,4) DEFAULT '0.8500' COMMENT '相似度阈值',
  `new_cat_threshold` decimal(5,4) DEFAULT '0.7000' COMMENT '新猫判断阈值',
  `top_k` int DEFAULT '5' COMMENT '返回结果数量',
  `notification_enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用通知',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`user_id`),
  KEY `idx_user_shadow_config_enabled` (`enabled`),
  CONSTRAINT `fk_user_shadow_config_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户影子模式配置表 - 使用user_id作为主键，每用户一条配置';


-- cats_db.shadow_mode_notifications definition

CREATE TABLE `shadow_mode_notifications` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` varchar(20) NOT NULL COMMENT '用户ID',
  `video_id` varchar(64) NOT NULL COMMENT '视频ID',
  `notification_type` enum('new_cat','different_result','low_confidence') NOT NULL COMMENT '通知类型',
  `message_title` varchar(255) NOT NULL COMMENT '通知标题',
  `message_content` text COMMENT '通知内容',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  CONSTRAINT `fk_shadow_notifications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='影子模式通知记录表 - 记录影子模式触发的通知';


-- cats_db.shadow_mode_analysis_view definition

CREATE VIEW `shadow_mode_analysis_view` AS
SELECT
    `ra`.`video_id` AS `video_id`,
    `ra`.`animal_id` AS `animal_id`,
    `ra`.`cat_confidence` AS `cat_confidence`,
    `ra`.`shadow_mode_result` AS `shadow_mode_result`,
    `ra`.`shadow_similarity` AS `shadow_similarity`,
    `ra`.`shadow_matched_cat_id` AS `shadow_matched_cat_id`,
    `ra`.`shadow_is_new_cat` AS `shadow_is_new_cat`,
    `ra`.`shadow_confidence` AS `shadow_confidence`,
    `ra`.`shadow_model_version` AS `shadow_model_version`,
    `rs`.`device_id` AS `device_id`,
    `rs`.`start_time` AS `start_time`,
    `rs`.`end_time` AS `end_time`,
    `d`.`user_id` AS `user_id`,
    `c`.`name` AS `original_cat_name`,
    `sc`.`name` AS `shadow_matched_cat_name`
FROM (((((`record_analysis` `ra`
    JOIN `record_shit` `rs` ON((`ra`.`video_id` = `rs`.`video_id`)))
    JOIN `devices` `d` ON((`rs`.`device_id` = `d`.`device_id`)))
    LEFT JOIN `cats` `c` ON((`ra`.`animal_id` = `c`.`cat_id`)))
    LEFT JOIN `cats` `sc` ON((`ra`.`shadow_matched_cat_id` = `sc`.`cat_id`)))
WHERE (`ra`.`shadow_mode_result` IS NOT NULL);