package api

import (
	"cabycare-server/pkg/cattoilet"

	"github.com/gin-gonic/gin"
)

// RegisterShadowRoutes 注册影子模式相关路由
func RegisterShadowRoutes(r *gin.RouterGroup, handler *cattoilet.Handler) {
	shadow := r.Group("/shadow")
	{
		// 影子模式健康检查
		shadow.GET("/health", handler.ShadowHealthCheck)
		
		// 初始化影子模式猫咪特征
		shadow.POST("/init-cats", handler.InitShadowCats)
		
		// 测试影子模式相似度
		shadow.POST("/test-similarity", handler.TestShadowSimilarity)
	}
}
