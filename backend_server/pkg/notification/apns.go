package notification

import (
	"cabycare-server/config"
	"context"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"os"
	"time"

	"github.com/sideshow/apns2"
	"github.com/sideshow/apns2/token"
)

type APNSProvider struct {
	client   *apns2.Client
	bundleID string
}

func NewAPNSProvider(cfg *config.Config) (*APNSProvider, error) {
	// 如果配置为空，返回空的provider（用于测试）
	if cfg.APS.AuthKeyPath == "" || cfg.APS.TeamID == "" || cfg.APS.KeyID == "" {
		return &APNSProvider{
			client:   nil,
			bundleID: cfg.APS.BundleID,
		}, nil
	}

	// 读取 APNs 认证密钥
	authKeyBytes, err := os.ReadFile(cfg.APS.AuthKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read APNs auth key: %v", err)
	}

	block, _ := pem.Decode(authKeyBytes)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	key, ok := privateKey.(*ecdsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("expected ECDSA private key")
	}

	// 创建 JWT token
	authToken := &token.Token{
		AuthKey: key,
		KeyID:   cfg.APS.KeyID,
		TeamID:  cfg.APS.TeamID,
	}

	// 创建 APNS 客户端
	client := apns2.NewTokenClient(authToken)

	// 默认使用生产环境
	client.Production()

	return &APNSProvider{
		client:   client,
		bundleID: cfg.APS.BundleID,
	}, nil
}

func (p *APNSProvider) Send(ctx context.Context, n *Notification, token DeviceToken) error {
	// 如果client为空（测试模式），直接返回成功
	if p.client == nil {
		return nil
	}

	// 根据设备令牌的环境设置客户端环境
	if token.Environment == "sandbox" {
		p.client.Development()
	} else {
		p.client.Production()
	}

	// 构造推送负载
	payload := map[string]interface{}{
		"aps": map[string]interface{}{
			"alert": map[string]interface{}{
				"title": n.Title,
				"body":  n.Body,
			},
			"sound": "default",
		},
		"type":      string(n.Type),
		"subtype":   string(n.SubType),
		"id":        n.NoticeID, // 使用 NoticeID 而不是 ID
		"timestamp": time.Now().Unix(),
	}

	if n.Metadata != nil {
		payload["metadata"] = json.RawMessage(n.Metadata)
	}

	// 创建通知
	notification := &apns2.Notification{
		DeviceToken: token.Token,
		Topic:       p.bundleID,
		Payload:     payload,
	}

	// 发送通知
	res, err := p.client.Push(notification)
	if err != nil {
		return fmt.Errorf("failed to send APNS notification: %v", err)
	}

	if res.Sent() {
		return nil
	}

	return fmt.Errorf("APNS send failed: %v", res.Reason)
}
