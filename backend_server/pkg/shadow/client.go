package shadow

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// Client 影子模式客户端
type Client struct {
	baseURL    string
	authToken  string
	httpClient *http.Client
}

// NewClient 创建新的影子模式客户端
func NewClient(baseURL, authToken string) *Client {
	return &Client{
		baseURL:   baseURL,
		authToken: authToken,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// SimilarityRequest 相似度查询请求
type SimilarityRequest struct {
	UserID      string `json:"user_id"`
	ImageBase64 string `json:"image_base64"`
	Limit       int    `json:"limit"`
}

// SimilarityResult 相似度查询结果
type SimilarityResult struct {
	Similarity float64                `json:"similarity"`
	Payload    map[string]interface{} `json:"payload"`
}

// SimilarityResponse 相似度查询响应
type SimilarityResponse struct {
	Success bool               `json:"success"`
	Results []SimilarityResult `json:"results"`
	Error   string             `json:"error,omitempty"`
}

// InitCatRequest 初始化猫咪请求
type InitCatRequest struct {
	UserID string    `json:"user_id"`
	Cats   []CatInfo `json:"cats"`
}

// CatInfo 猫咪信息
type CatInfo struct {
	CatID       string `json:"cat_id"`
	Name        string `json:"name"`
	ImageBase64 string `json:"image_base64"`
}

// InitCatResult 初始化猫咪结果
type InitCatResult struct {
	CatID   string `json:"cat_id"`
	Name    string `json:"name"`
	Success bool   `json:"success"`
	Error   string `json:"error,omitempty"`
}

// InitCatResponse 初始化猫咪响应
type InitCatResponse struct {
	Success bool            `json:"success"`
	Results []InitCatResult `json:"results"`
	Error   string          `json:"error,omitempty"`
}

// TestSimilarity 测试相似度查询
func (c *Client) TestSimilarity(ctx context.Context, req *SimilarityRequest) (*SimilarityResponse, error) {
	url := fmt.Sprintf("%s/api/v1/admin/test-similarity", c.baseURL)
	
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}
	
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.authToken))
	
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}
	
	var result SimilarityResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	
	return &result, nil
}

// InitCats 初始化猫咪特征
func (c *Client) InitCats(ctx context.Context, req *InitCatRequest) (*InitCatResponse, error) {
	url := fmt.Sprintf("%s/api/v1/admin/init-cats", c.baseURL)
	
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}
	
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}
	
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.authToken))
	
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}
	
	var result InitCatResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	
	return &result, nil
}

// HealthCheck 健康检查
func (c *Client) HealthCheck(ctx context.Context) error {
	url := fmt.Sprintf("%s/health", c.baseURL)
	
	httpReq, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("health check failed with status %d: %s", resp.StatusCode, string(body))
	}
	
	return nil
}
