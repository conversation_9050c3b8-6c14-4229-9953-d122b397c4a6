package shadow

import (
	"cabycare-server/config"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// Service 影子模式服务
type Service struct {
	client *Client
	config *config.Config
}

// NewService 创建新的影子模式服务
func NewService(cfg *config.Config) *Service {
	client := NewClient(cfg.CabyAI.URL, cfg.CabyAI.AuthToken)
	
	return &Service{
		client: client,
		config: cfg,
	}
}

// ShadowModeResult 影子模式结果
type ShadowModeResult struct {
	CatID       string  `json:"cat_id"`
	CatName     string  `json:"cat_name"`
	Similarity  float64 `json:"similarity"`
	Confidence  float64 `json:"confidence"`
	IsNewCat    bool    `json:"is_new_cat"`
	ModelVersion string `json:"model_version"`
}

// ProcessImage 处理图像进行影子模式识别
func (s *Service) ProcessImage(ctx context.Context, imageBase64, userID string) (*ShadowModeResult, error) {
	if !s.config.ShadowMode.Enabled {
		return nil, fmt.Errorf("shadow mode is disabled")
	}
	
	// 调用caby_ai的相似度查询API
	req := &SimilarityRequest{
		UserID:      userID,
		ImageBase64: imageBase64,
		Limit:       s.config.ShadowMode.TopK,
	}
	
	resp, err := s.client.TestSimilarity(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to query similarity: %w", err)
	}
	
	if !resp.Success {
		return nil, fmt.Errorf("similarity query failed: %s", resp.Error)
	}
	
	// 分析结果
	result := s.analyzeResults(resp.Results)
	return result, nil
}

// analyzeResults 分析相似度查询结果
func (s *Service) analyzeResults(results []SimilarityResult) *ShadowModeResult {
	if len(results) == 0 {
		// 没有找到相似的猫咪，可能是新猫
		return &ShadowModeResult{
			CatID:      "unknown",
			CatName:    "Unknown Cat",
			Similarity: 0.0,
			Confidence: 1.0, // 高置信度认为是新猫
			IsNewCat:   true,
			ModelVersion: "featured_v1.0",
		}
	}
	
	// 获取最相似的结果
	topResult := results[0]
	
	// 从payload中提取信息
	catID := "unknown"
	catName := "Unknown Cat"
	
	if payload := topResult.Payload; payload != nil {
		if id, ok := payload["cat_id"].(string); ok {
			catID = id
		}
		if name, ok := payload["cat_name"].(string); ok {
			catName = name
		}
	}
	
	// 根据相似度阈值判断
	similarity := topResult.Similarity
	
	if similarity >= s.config.ShadowMode.SimilarityThreshold {
		// 高相似度，认为是已知猫咪
		return &ShadowModeResult{
			CatID:      catID,
			CatName:    catName,
			Similarity: similarity,
			Confidence: similarity,
			IsNewCat:   false,
			ModelVersion: "featured_v1.0",
		}
	} else if similarity >= s.config.ShadowMode.NewCatThreshold {
		// 中等相似度，不确定
		return &ShadowModeResult{
			CatID:      "uncertain",
			CatName:    fmt.Sprintf("Uncertain (%s?)", catName),
			Similarity: similarity,
			Confidence: 0.5,
			IsNewCat:   false,
			ModelVersion: "featured_v1.0",
		}
	} else {
		// 低相似度，可能是新猫
		return &ShadowModeResult{
			CatID:      "unknown",
			CatName:    "Unknown Cat",
			Similarity: similarity,
			Confidence: 1.0 - similarity, // 新猫的置信度
			IsNewCat:   true,
			ModelVersion: "featured_v1.0",
		}
	}
}

// InitializeCatsFromImages 从图片目录初始化猫咪特征
func (s *Service) InitializeCatsFromImages(ctx context.Context, userID string, catsConfig []CatConfig) error {
	if !s.config.ShadowMode.Enabled {
		return fmt.Errorf("shadow mode is disabled")
	}
	
	var cats []CatInfo
	
	for _, catConfig := range catsConfig {
		// 读取图片文件
		imageData, err := os.ReadFile(catConfig.ImagePath)
		if err != nil {
			log.Printf("Failed to read image for cat %s: %v", catConfig.Name, err)
			continue
		}
		
		// 转换为base64
		imageBase64 := base64.StdEncoding.EncodeToString(imageData)
		
		cats = append(cats, CatInfo{
			CatID:       catConfig.CatID,
			Name:        catConfig.Name,
			ImageBase64: imageBase64,
		})
		
		log.Printf("Prepared cat %s (ID: %s) for initialization", catConfig.Name, catConfig.CatID)
	}
	
	if len(cats) == 0 {
		return fmt.Errorf("no valid cat images found")
	}
	
	// 调用初始化API
	req := &InitCatRequest{
		UserID: userID,
		Cats:   cats,
	}
	
	resp, err := s.client.InitCats(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to initialize cats: %w", err)
	}
	
	if !resp.Success {
		return fmt.Errorf("cat initialization failed: %s", resp.Error)
	}
	
	// 检查结果
	for _, result := range resp.Results {
		if result.Success {
			log.Printf("Successfully initialized cat %s (ID: %s)", result.Name, result.CatID)
		} else {
			log.Printf("Failed to initialize cat %s (ID: %s): %s", result.Name, result.CatID, result.Error)
		}
	}
	
	return nil
}

// CatConfig 猫咪配置
type CatConfig struct {
	CatID     string `json:"cat_id"`
	Name      string `json:"name"`
	ImagePath string `json:"image_path"`
}

// LoadCatsFromDirectory 从目录加载猫咪配置
func (s *Service) LoadCatsFromDirectory(dirPath string) ([]CatConfig, error) {
	var configs []CatConfig
	
	// 遍历目录中的图片文件
	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if info.IsDir() {
			return nil
		}
		
		// 检查是否是图片文件
		ext := strings.ToLower(filepath.Ext(path))
		if ext != ".jpg" && ext != ".jpeg" && ext != ".png" {
			return nil
		}
		
		// 从文件名生成配置
		filename := strings.TrimSuffix(info.Name(), ext)
		
		config := CatConfig{
			CatID:     fmt.Sprintf("cat_%s_%d", filename, time.Now().Unix()),
			Name:      filename,
			ImagePath: path,
		}
		
		configs = append(configs, config)
		return nil
	})
	
	if err != nil {
		return nil, fmt.Errorf("failed to walk directory: %w", err)
	}
	
	return configs, nil
}

// HealthCheck 健康检查
func (s *Service) HealthCheck(ctx context.Context) error {
	return s.client.HealthCheck(ctx)
}

// IsEnabled 检查影子模式是否启用
func (s *Service) IsEnabled() bool {
	return s.config.ShadowMode.Enabled
}
