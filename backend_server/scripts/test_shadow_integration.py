#!/usr/bin/env python3
"""
Backend Server 影子模式完整集成测试
包括启动backend_server、测试API、验证数据流
"""

import requests
import json
import base64
import os
import sys
import time
import subprocess
import signal
import tempfile
from typing import Dict, Any

class ShadowModeIntegrationTester:
    def __init__(self, backend_url: str = "http://localhost:5678", caby_ai_url: str = "http://localhost:8765"):
        self.backend_url = backend_url
        self.caby_ai_url = caby_ai_url
        self.backend_process = None
        self.test_config_path = None

    def create_test_config(self) -> str:
        """创建测试配置文件"""
        print("📝 创建测试配置文件...")
        
        config_content = """
server:
  port: 5678
  mode: "debug"

database:
  host: "localhost"
  port: 3306
  user: "root"
  password: "123456"
  dbname: "cabycare"
  charset: "utf8mb4"
  parse_time: true
  loc: "Local"

mysql:
  host: "localhost"
  port: 3306
  user: "root"
  password: "123456"
  database: "cabycare"

minio:
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  use_ssl: false
  bucket_name: "cabycare"

minio_ota:
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  use_ssl: false

# 禁用通知服务进行测试
aps:
  team_id: ""
  key_id: ""
  bundle_id: ""
  auth_key_path: ""

logto:
  endpoint: ""
  app_id: ""
  app_secret: ""
  api_resource: ""
  callback_uri: ""

caby_backend:
  url: "https://api.caby.care"

caby_ai:
  url: "http://localhost:8765"
  auth_token: "03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
  max_retries: 300
  timeout_minutes: 30

# Shadow mode configuration
shadow_mode:
  enabled: true
  similarity_threshold: 0.85
  new_cat_threshold: 0.70
  top_k: 5
  notification_enabled: false

# Device monitor configuration
device_config:
  heartbeat_interval: 15
  status_timeout: 31
  statistics_window: 24
"""
        
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write(config_content.strip())
            self.test_config_path = f.name
        
        print(f"   ✅ 测试配置文件创建: {self.test_config_path}")
        return self.test_config_path

    def start_backend_server(self) -> bool:
        """启动backend server"""
        print("🚀 启动Backend Server...")
        
        if not self.test_config_path:
            self.create_test_config()
        
        try:
            # 设置环境变量指向测试配置
            env = os.environ.copy()
            env['CONFIG_PATH'] = self.test_config_path
            
            # 启动backend server
            self.backend_process = subprocess.Popen(
                ["./backend_server"],
                cwd="/home/<USER>/animsi/aby/server/backend_server",
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=env
            )
            
            # 等待服务启动
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get(f"{self.backend_url}/api/health", timeout=1)
                    if response.status_code == 200:
                        print("   ✅ Backend Server启动成功")
                        return True
                except:
                    pass
                time.sleep(1)
            
            print("   ❌ Backend Server启动超时")
            return False
            
        except Exception as e:
            print(f"   ❌ 启动Backend Server失败: {e}")
            return False

    def stop_backend_server(self):
        """停止backend server"""
        if self.backend_process:
            print("🛑 停止Backend Server...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
            print("   ✅ Backend Server已停止")
        
        # 清理测试配置文件
        if self.test_config_path and os.path.exists(self.test_config_path):
            os.unlink(self.test_config_path)

    def test_caby_ai_connection(self) -> bool:
        """测试caby_ai连接"""
        print("🔍 测试caby_ai服务连接...")
        
        try:
            response = requests.get(f"{self.caby_ai_url}/health", timeout=5)
            if response.status_code == 200:
                print("   ✅ caby_ai服务连接正常")
                return True
            else:
                print(f"   ❌ caby_ai服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ caby_ai服务连接失败: {e}")
            return False

    def test_detect_cat_direct(self) -> bool:
        """直接测试caby_ai的/detect/cat接口"""
        print("🔍 测试caby_ai /detect/cat接口...")
        
        test_image_path = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
        
        if not os.path.exists(test_image_path):
            print(f"   ❌ 测试图片不存在: {test_image_path}")
            return False
        
        headers = {"Authorization": "Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga"}
        
        try:
            with open(test_image_path, 'rb') as f:
                files = {'image': ('test_cat.jpg', f, 'image/jpeg')}
                data = {
                    'task': 'predict',
                    'return_features': 'false',
                    'return_confidence': 'true'
                }
                
                response = requests.post(
                    f"{self.caby_ai_url}/api/v1/vision/detect/cat",
                    files=files,
                    data=data,
                    headers=headers,
                    timeout=30
                )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ /detect/cat接口正常")
                    print(f"      预测猫咪: {result.get('predicted_cat')}")
                    print(f"      置信度: {result.get('confidence', 0):.4f}")
                    return True
                else:
                    print(f"   ❌ /detect/cat接口返回失败: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ /detect/cat接口调用失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ /detect/cat接口调用异常: {e}")
            return False

    def test_backend_shadow_api(self) -> bool:
        """测试backend_server的影子模式API"""
        print("🔍 测试Backend Server影子模式API...")
        
        # 1. 健康检查
        try:
            response = requests.get(f"{self.backend_url}/api/shadow/health", timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 影子模式健康检查成功")
                print(f"      状态: {result.get('status')}")
                print(f"      启用: {result.get('enabled')}")
            else:
                print(f"   ❌ 影子模式健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"   ❌ 影子模式健康检查异常: {e}")
            return False
        
        # 2. 测试相似度查询（使用DetectCat接口）
        test_image_path = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
        
        if not os.path.exists(test_image_path):
            print(f"   ❌ 测试图片不存在: {test_image_path}")
            return False
        
        try:
            with open(test_image_path, 'rb') as f:
                image_data = f.read()
                image_base64 = base64.b64encode(image_data).decode()
        except Exception as e:
            print(f"   ❌ 图片转换失败: {e}")
            return False
        
        similarity_data = {
            "user_id": "test_user_123",
            "image_base64": image_base64
        }
        
        try:
            response = requests.post(
                f"{self.backend_url}/api/shadow/test-similarity",
                json=similarity_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"   ✅ 影子模式相似度查询成功")
                    print(f"      猫咪ID: {result.get('cat_id')}")
                    print(f"      猫咪名称: {result.get('cat_name')}")
                    print(f"      相似度: {result.get('similarity', 0):.4f}")
                    print(f"      置信度: {result.get('confidence', 0):.4f}")
                    print(f"      是否新猫: {result.get('is_new_cat')}")
                    return True
                else:
                    print(f"   ❌ 影子模式相似度查询返回失败: {result.get('error')}")
                    return False
            else:
                print(f"   ❌ 影子模式相似度查询失败: HTTP {response.status_code}")
                print(f"      响应: {response.text}")
                return False
        except Exception as e:
            print(f"   ❌ 影子模式相似度查询异常: {e}")
            return False

    def run_integration_test(self) -> bool:
        """运行完整集成测试"""
        print("🚀 开始Backend Server影子模式完整集成测试")
        print("=" * 70)
        
        try:
            # 1. 测试caby_ai连接
            if not self.test_caby_ai_connection():
                return False
            print()
            
            # 2. 直接测试caby_ai接口
            if not self.test_detect_cat_direct():
                return False
            print()
            
            # 3. 启动backend_server
            if not self.start_backend_server():
                return False
            print()
            
            # 4. 测试backend_server影子模式API
            if not self.test_backend_shadow_api():
                return False
            print()
            
            print("=" * 70)
            print("🎉 所有集成测试通过！Backend Server影子模式完全正常！")
            print("\n📋 测试总结:")
            print("   ✅ caby_ai服务连接正常")
            print("   ✅ caby_ai /detect/cat接口正常")
            print("   ✅ Backend Server启动成功")
            print("   ✅ Backend Server影子模式API正常")
            print("   ✅ 数据发送、接收、解析流程完整")
            print("\n🚀 Backend Server影子模式已完全准备就绪！")
            
            return True
            
        finally:
            self.stop_backend_server()

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Backend Server影子模式完整集成测试")
    parser.add_argument("--backend-url", default="http://localhost:5678", help="Backend Server URL")
    parser.add_argument("--caby-ai-url", default="http://localhost:8765", help="Caby AI URL")
    
    args = parser.parse_args()
    
    tester = ShadowModeIntegrationTester(args.backend_url, args.caby_ai_url)
    
    # 设置信号处理器，确保服务器能够正确停止
    def signal_handler(sig, frame):
        print("\n🛑 收到中断信号，正在停止服务...")
        tester.stop_backend_server()
        sys.exit(1)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    success = tester.run_integration_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
