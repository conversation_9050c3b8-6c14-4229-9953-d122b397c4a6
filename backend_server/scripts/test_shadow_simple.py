#!/usr/bin/env python3
"""
Backend Server 影子模式简化测试
直接测试影子模式客户端功能，不启动完整的backend_server
"""

import subprocess
import sys
import os
import tempfile

def test_shadow_client():
    """测试影子模式客户端"""
    print("🚀 测试Backend Server影子模式客户端功能")
    print("=" * 60)
    
    # 创建Go测试程序
    test_go_code = '''
package main

import (
	"cabycare-server/config"
	"cabycare-server/pkg/shadow"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 创建影子模式服务
	shadowService := shadow.NewService(cfg)

	// 测试健康检查
	fmt.Println("🔍 测试影子模式健康检查...")
	ctx := context.Background()
	
	err = shadowService.HealthCheck(ctx)
	if err != nil {
		fmt.Printf("❌ 健康检查失败: %v\\n", err)
		return
	}
	fmt.Println("✅ 健康检查成功")

	// 测试图片处理
	fmt.Println("\\n🔍 测试影子模式图片处理...")
	
	testImagePath := "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
	
	// 读取测试图片
	imageData, err := os.ReadFile(testImagePath)
	if err != nil {
		fmt.Printf("❌ 读取测试图片失败: %v\\n", err)
		return
	}

	imageBase64 := base64.StdEncoding.EncodeToString(imageData)
	userID := "test_user_123"
	
	// 测试ProcessImage方法
	result, err := shadowService.ProcessImage(ctx, imageBase64, userID)
	if err != nil {
		fmt.Printf("❌ ProcessImage失败: %v\\n", err)
		return
	}

	fmt.Printf("✅ ProcessImage成功:\\n")
	fmt.Printf("   猫咪ID: %s\\n", result.CatID)
	fmt.Printf("   猫咪名称: %s\\n", result.CatName)
	fmt.Printf("   相似度: %.4f\\n", result.Similarity)
	fmt.Printf("   置信度: %.4f\\n", result.Confidence)
	fmt.Printf("   是否新猫: %v\\n", result.IsNewCat)
	fmt.Printf("   模型版本: %s\\n", result.ModelVersion)
	
	fmt.Println("\\n🎉 所有测试通过！影子模式客户端工作正常！")
}
'''
    
    # 写入测试文件
    test_file_path = "/home/<USER>/animsi/aby/server/backend_server/test_shadow_simple.go"
    try:
        with open(test_file_path, 'w') as f:
            f.write(test_go_code)
        
        print("📝 创建Go测试程序...")
        print("🚀 运行影子模式客户端测试...")
        
        # 运行测试
        result = subprocess.run(
            ["go", "run", "test_shadow_simple.go"],
            cwd="/home/<USER>/animsi/aby/server/backend_server",
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ Backend影子模式客户端测试成功")
            print("\n📊 测试输出:")
            for line in result.stdout.strip().split('\n'):
                print(f"   {line}")
            
            print("\n" + "=" * 60)
            print("🎉 Backend Server影子模式客户端功能完全正常！")
            print("\n📋 功能验证:")
            print("   ✅ 影子模式服务初始化正常")
            print("   ✅ caby_ai连接正常")
            print("   ✅ DetectCat接口调用正常")
            print("   ✅ 数据发送、接收、解析流程完整")
            print("   ✅ 结果分析逻辑正常")
            print("\n🚀 Backend Server影子模式已完全准备就绪！")
            
            return True
        else:
            print("❌ Backend影子模式客户端测试失败")
            print(f"\n错误输出:")
            for line in result.stderr.strip().split('\n'):
                print(f"   {line}")
            print(f"\n标准输出:")
            for line in result.stdout.strip().split('\n'):
                print(f"   {line}")
            return False
            
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_caby_ai_detect_cat():
    """测试caby_ai的/detect/cat接口"""
    print("🔍 测试caby_ai /detect/cat接口...")
    
    import requests
    import base64
    
    test_image_path = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
    
    if not os.path.exists(test_image_path):
        print(f"   ❌ 测试图片不存在: {test_image_path}")
        return False
    
    headers = {"Authorization": "Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga"}
    
    try:
        with open(test_image_path, 'rb') as f:
            files = {'image': ('test_cat.jpg', f, 'image/jpeg')}
            data = {
                'task': 'predict',
                'return_features': 'false',
                'return_confidence': 'true'
            }
            
            response = requests.post(
                "http://localhost:8765/api/v1/vision/detect/cat",
                files=files,
                data=data,
                headers=headers,
                timeout=30
            )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"   ✅ /detect/cat接口正常")
                print(f"      预测猫咪: {result.get('predicted_cat')}")
                print(f"      置信度: {result.get('confidence', 0):.4f}")
                print(f"      处理时间: {result.get('process_time', 0):.2f}ms")
                return True
            else:
                print(f"   ❌ /detect/cat接口返回失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ /detect/cat接口调用失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ /detect/cat接口调用异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Backend Server影子模式简化测试")
    print("=" * 70)
    
    tests = [
        ("caby_ai /detect/cat接口测试", test_caby_ai_detect_cat),
        ("Backend影子模式客户端测试", test_shadow_client),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 70)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Backend Server影子模式功能完全正常！")
        print("\n📋 最终验证:")
        print("   ✅ caby_ai /detect/cat接口正常工作")
        print("   ✅ Backend影子模式客户端正常工作")
        print("   ✅ DetectCat方法正确调用caby_ai接口")
        print("   ✅ 数据发送、接收、解析流程完整无误")
        print("   ✅ 影子模式结果分析逻辑正确")
        print("\n🚀 Backend Server影子模式已完全准备就绪，可以投入使用！")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
