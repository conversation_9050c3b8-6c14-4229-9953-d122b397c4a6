#!/usr/bin/env python3
"""
Backend Server 影子模式测试脚本
"""

import requests
import json
import base64
import os
import sys
from typing import Dict, Any

class BackendShadowTester:
    def __init__(self, base_url: str = "http://localhost:5678", auth_token: str = None):
        self.base_url = base_url
        self.auth_token = auth_token
        self.session = requests.Session()
        
        if auth_token:
            self.session.headers.update({
                "Authorization": f"Bearer {auth_token}"
            })

    def test_shadow_health(self) -> bool:
        """测试影子模式健康检查"""
        print("🔍 测试影子模式健康检查...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/shadow/health")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 健康检查成功")
                print(f"   状态: {result.get('status')}")
                print(f"   启用状态: {result.get('enabled')}")
                print(f"   消息: {result.get('message')}")
                return True
            else:
                print(f"   ❌ 健康检查失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 健康检查异常: {e}")
            return False

    def test_init_shadow_cats(self, user_id: str = "0220280ee0021000") -> bool:
        """测试初始化影子模式猫咪"""
        print(f"🔍 测试初始化影子模式猫咪 (用户: {user_id})...")
        
        # 猫咪配置
        cats_config = [
            {
                "cat_id": "f3ce1b02b5f1a8b9d31000",
                "name": "小花",
                "image_path": "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
            },
            {
                "cat_id": "f3ce1b02b2c1d755421000",
                "name": "小黑",
                "image_path": "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-04-03_10-52-02_hls.jpg"
            },
            {
                "cat_id": "f3ce1b02b40e9477c21000",
                "name": "小白",
                "image_path": "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-27_12-32-19_hls.jpg"
            }
        ]
        
        request_data = {
            "user_id": user_id,
            "cats": cats_config
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/shadow/init-cats",
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 初始化成功")
                print(f"   消息: {result.get('message')}")
                return True
            else:
                print(f"   ❌ 初始化失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 初始化异常: {e}")
            return False

    def test_shadow_similarity(self, user_id: str = "0220280ee0021000") -> bool:
        """测试影子模式相似度"""
        print(f"🔍 测试影子模式相似度 (用户: {user_id})...")
        
        # 使用一张真实的猫咪图片
        test_image_path = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
        
        if not os.path.exists(test_image_path):
            print(f"   ❌ 测试图片不存在: {test_image_path}")
            return False
        
        # 读取并转换图片为base64
        try:
            with open(test_image_path, 'rb') as f:
                image_data = f.read()
                image_base64 = base64.b64encode(image_data).decode()
        except Exception as e:
            print(f"   ❌ 图片转换失败: {e}")
            return False
        
        request_data = {
            "user_id": user_id,
            "image_base64": image_base64
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/api/shadow/test-similarity",
                json=request_data
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 相似度测试成功")
                print(f"   猫咪ID: {result.get('cat_id')}")
                print(f"   猫咪名称: {result.get('cat_name')}")
                print(f"   相似度: {result.get('similarity'):.4f}")
                print(f"   置信度: {result.get('confidence'):.4f}")
                print(f"   是否新猫: {result.get('is_new_cat')}")
                print(f"   模型版本: {result.get('model_version')}")
                return True
            else:
                print(f"   ❌ 相似度测试失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 相似度测试异常: {e}")
            return False

    def run_all_tests(self, user_id: str = "0220280ee0021000") -> bool:
        """运行所有测试"""
        print("🚀 开始Backend Server影子模式测试")
        print("=" * 60)
        
        tests = [
            ("健康检查", lambda: self.test_shadow_health()),
            ("初始化猫咪", lambda: self.test_init_shadow_cats(user_id)),
            ("相似度测试", lambda: self.test_shadow_similarity(user_id)),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 {test_name}")
            print("-" * 40)
            
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        
        print("\n" + "=" * 60)
        print(f"📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！Backend Server影子模式工作正常！")
            return True
        else:
            print("⚠️  部分测试失败，请检查配置和服务状态")
            return False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Backend Server影子模式测试")
    parser.add_argument("--url", default="http://localhost:5678", help="Backend Server URL")
    parser.add_argument("--auth-token", help="认证令牌")
    parser.add_argument("--user-id", default="0220280ee0021000", help="测试用户ID")
    
    args = parser.parse_args()
    
    tester = BackendShadowTester(args.url, args.auth_token)
    
    success = tester.run_all_tests(args.user_id)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
