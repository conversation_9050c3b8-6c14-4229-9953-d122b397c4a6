#!/usr/bin/env python3
"""
测试Backend Server的影子模式客户端功能
"""

import requests
import json
import base64
import os
import sys

def test_shadow_client():
    """测试影子模式客户端功能"""
    print("🚀 测试Backend Server影子模式客户端功能")
    print("=" * 60)
    
    # 测试caby_ai服务连接
    print("🔍 测试caby_ai服务连接...")
    try:
        response = requests.get("http://localhost:8765/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ caby_ai服务连接正常")
        else:
            print(f"   ❌ caby_ai服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ caby_ai服务连接失败: {e}")
        return False
    
    # 测试影子模式健康检查
    print("\n🔍 测试影子模式健康检查...")
    headers = {
        "Authorization": "Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get("http://localhost:8765/health", headers=headers, timeout=10)
        if response.status_code == 200:
            print("   ✅ 影子模式健康检查成功")
        else:
            print(f"   ❌ 影子模式健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 影子模式健康检查异常: {e}")
        return False
    
    # 测试初始化猫咪特征
    print("\n🔍 测试初始化猫咪特征...")
    user_id = "0220280ee0021000"
    
    cats_data = []
    cats_config = [
        {
            "cat_id": "f3ce1b02b5f1a8b9d31000",
            "name": "小花",
            "image_path": "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
        },
        {
            "cat_id": "f3ce1b02b2c1d755421000",
            "name": "小黑",
            "image_path": "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-04-03_10-52-02_hls.jpg"
        },
        {
            "cat_id": "f3ce1b02b40e9477c21000",
            "name": "小白",
            "image_path": "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-27_12-32-19_hls.jpg"
        }
    ]
    
    # 转换图片为base64
    for config in cats_config:
        if not os.path.exists(config["image_path"]):
            print(f"   ❌ 图片文件不存在: {config['image_path']}")
            return False
        
        try:
            with open(config["image_path"], 'rb') as f:
                image_data = f.read()
                image_base64 = base64.b64encode(image_data).decode()
                
            cats_data.append({
                "cat_id": config["cat_id"],
                "name": config["name"],
                "image_base64": image_base64
            })
            print(f"   📷 {config['name']} 图片转换成功")
        except Exception as e:
            print(f"   ❌ {config['name']} 图片转换失败: {e}")
            return False
    
    # 发送初始化请求
    init_request = {
        "user_id": user_id,
        "cats": cats_data
    }
    
    try:
        response = requests.post(
            "http://localhost:8765/api/v1/admin/init-cats",
            headers=headers,
            json=init_request,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("   ✅ 猫咪特征初始化成功")
                for cat_result in result.get("results", []):
                    if cat_result.get("success"):
                        print(f"      ✅ {cat_result['name']} (ID: {cat_result['cat_id']})")
                    else:
                        print(f"      ❌ {cat_result['name']} (ID: {cat_result['cat_id']}): {cat_result.get('error')}")
            else:
                print(f"   ❌ 猫咪特征初始化失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 初始化请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 初始化请求异常: {e}")
        return False
    
    # 测试相似度查询
    print("\n🔍 测试相似度查询...")
    
    # 使用小花的图片进行测试
    test_image_path = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
    
    try:
        with open(test_image_path, 'rb') as f:
            image_data = f.read()
            test_image_base64 = base64.b64encode(image_data).decode()
    except Exception as e:
        print(f"   ❌ 测试图片转换失败: {e}")
        return False
    
    similarity_request = {
        "user_id": user_id,
        "image_base64": test_image_base64,
        "limit": 5
    }
    
    try:
        response = requests.post(
            "http://localhost:8765/api/v1/admin/test-similarity",
            headers=headers,
            json=similarity_request,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                results = result.get("results", [])
                print(f"   ✅ 相似度查询成功，找到 {len(results)} 个结果")
                
                for i, item in enumerate(results[:3], 1):
                    payload = item.get("payload", {})
                    cat_id = payload.get("cat_id", "unknown")
                    similarity = item.get("similarity", 0)
                    print(f"      {i}. 猫咪ID: {cat_id}, 相似度: {similarity:.4f}")
                
                # 检查是否正确识别为小花
                if results and results[0].get("payload", {}).get("cat_id") == "f3ce1b02b5f1a8b9d31000":
                    print("   🎯 正确识别为小花！")
                else:
                    print("   ⚠️  识别结果可能不准确")
                
            else:
                print(f"   ❌ 相似度查询失败: {result.get('error')}")
                return False
        else:
            print(f"   ❌ 相似度查询请求失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 相似度查询异常: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有测试通过！Backend Server影子模式客户端功能正常！")
    print("\n📋 测试总结:")
    print("   ✅ caby_ai服务连接正常")
    print("   ✅ 影子模式健康检查成功")
    print("   ✅ 猫咪特征初始化成功")
    print("   ✅ 相似度查询功能正常")
    print("\n🚀 Backend Server已准备好集成影子模式！")
    
    return True

def main():
    success = test_shadow_client()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
