#!/usr/bin/env python3
"""
测试Backend Server的影子模式DetectCat接口功能
"""

import requests
import json
import base64
import os
import sys

def test_detect_cat_api():
    """测试/detect/cat接口"""
    print("🚀 测试Backend Server影子模式DetectCat接口")
    print("=" * 60)
    
    # 测试caby_ai服务连接
    print("🔍 测试caby_ai服务连接...")
    try:
        response = requests.get("http://localhost:8765/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ caby_ai服务连接正常")
        else:
            print(f"   ❌ caby_ai服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ caby_ai服务连接失败: {e}")
        return False
    
    # 测试/detect/cat接口
    print("\n🔍 测试/detect/cat接口...")
    
    # 使用测试图片
    test_image_path = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
    
    if not os.path.exists(test_image_path):
        print(f"   ❌ 测试图片不存在: {test_image_path}")
        return False
    
    headers = {
        "Authorization": "Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga"
    }
    
    try:
        # 准备multipart/form-data请求
        with open(test_image_path, 'rb') as f:
            files = {
                'image': ('test_cat.jpg', f, 'image/jpeg')
            }
            data = {
                'task': 'predict',
                'return_features': 'false',
                'return_confidence': 'true'
            }
            
            response = requests.post(
                "http://localhost:8765/api/v1/vision/detect/cat",
                files=files,
                data=data,
                headers=headers,
                timeout=30
            )
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ /detect/cat接口调用成功")
            print(f"   📊 结果:")
            print(f"      成功状态: {result.get('success')}")
            print(f"      预测猫咪: {result.get('predicted_cat')}")
            print(f"      置信度: {result.get('confidence', 0):.4f}")
            print(f"      处理时间: {result.get('process_time', 0):.2f}ms")
            print(f"      任务类型: {result.get('task')}")
            print(f"      消息: {result.get('message')}")
            
            # 显示类别概率（如果有）
            if 'class_probabilities' in result:
                print(f"      类别概率:")
                for cat_name, prob in result['class_probabilities'].items():
                    print(f"        {cat_name}: {prob:.4f}")
            
            return result.get('success', False)
        else:
            print(f"   ❌ /detect/cat接口调用失败: HTTP {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ /detect/cat接口调用异常: {e}")
        return False

def test_backend_shadow_client():
    """测试backend_server的影子模式客户端"""
    print("\n🔍 测试Backend Server影子模式客户端...")
    
    # 创建一个简单的Go测试程序
    test_go_code = '''
package main

import (
	"cabycare-server/config"
	"cabycare-server/pkg/shadow"
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"os"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// 创建影子模式服务
	shadowService := shadow.NewService(cfg)

	// 测试图片路径
	testImagePath := "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails/2025-01-28_04-13-10_hls.jpg"
	
	// 读取测试图片
	imageData, err := os.ReadFile(testImagePath)
	if err != nil {
		fmt.Printf("❌ 读取测试图片失败: %v\\n", err)
		return
	}

	imageBase64 := base64.StdEncoding.EncodeToString(imageData)
	
	ctx := context.Background()
	userID := "test_user_123"
	
	// 测试ProcessImage方法
	result, err := shadowService.ProcessImage(ctx, imageBase64, userID)
	if err != nil {
		fmt.Printf("❌ ProcessImage失败: %v\\n", err)
		return
	}

	fmt.Printf("✅ ProcessImage成功:\\n")
	fmt.Printf("   猫咪ID: %s\\n", result.CatID)
	fmt.Printf("   猫咪名称: %s\\n", result.CatName)
	fmt.Printf("   相似度: %.4f\\n", result.Similarity)
	fmt.Printf("   置信度: %.4f\\n", result.Confidence)
	fmt.Printf("   是否新猫: %v\\n", result.IsNewCat)
	fmt.Printf("   模型版本: %s\\n", result.ModelVersion)
}
'''
    
    # 写入测试文件
    test_file_path = "/home/<USER>/animsi/aby/server/backend_server/test_shadow_detect.go"
    try:
        with open(test_file_path, 'w') as f:
            f.write(test_go_code)
        
        # 运行测试
        import subprocess
        result = subprocess.run(
            ["go", "run", "test_shadow_detect.go"],
            cwd="/home/<USER>/animsi/aby/server/backend_server",
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("   ✅ Backend影子模式客户端测试成功")
            print("   📊 输出:")
            for line in result.stdout.strip().split('\n'):
                print(f"      {line}")
            return True
        else:
            print("   ❌ Backend影子模式客户端测试失败")
            print(f"   错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ Backend影子模式客户端测试异常: {e}")
        return False
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def main():
    """主测试函数"""
    print("🚀 开始Backend Server影子模式DetectCat完整测试")
    print("=" * 70)
    
    tests = [
        ("DetectCat API测试", test_detect_cat_api),
        ("Backend影子模式客户端测试", test_backend_shadow_client),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 50)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 70)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Backend Server影子模式DetectCat功能正常！")
        print("\n📋 测试总结:")
        print("   ✅ caby_ai /detect/cat接口正常")
        print("   ✅ Backend影子模式客户端正常")
        print("   ✅ 数据发送、接收、解析流程完整")
        print("\n🚀 Backend Server影子模式已完全准备就绪！")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
