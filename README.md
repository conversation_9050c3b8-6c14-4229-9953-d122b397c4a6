# CabyCare 智能猫厕所后端服务

CabyCare 是一个智能猫厕所监控系统的后端服务，提供设备管理、视频分析、用户认证、通知推送等功能。

## 🏗️ 系统架构

### 核心组件

- **后端服务器**: Go + Gin 框架，提供 RESTful API
- **数据库**: MySQL 8.0，存储用户、设备、记录等数据
- **对象存储**: MinIO，存储视频文件和缩略图
- **认证服务**: Logto 集成，提供用户认证和授权
- **推送服务**: Apple Push Notification Service (APNs)
- **AI 分析**: 集成 Caby AI 和 Caby Vision 进行智能视频分析和猫咪个体识别
- **影子模式**: 基于特征向量的猫咪识别系统，支持新猫咪发现和渐进式模型替换，完全集成到后台流程中

### 服务模块

```
backend_server/
├── api/                    # API 路由定义
├── config/                 # 配置管理
├── database/              # 数据库脚本
├── pkg/
│   ├── auth/              # 认证服务
│   ├── cattoilet/         # 猫厕所核心业务（包含影子模式集成）
│   ├── notification/      # 通知推送服务
│   ├── shadow/            # 影子模式服务
│   ├── storage/           # 对象存储服务
│   ├── video/             # 视频处理服务
│   ├── types/             # 数据类型定义
│   └── utils/             # 工具函数
├── static/                # 静态文件
├── scripts/               # 部署脚本
└── main.go               # 应用入口
```

## 🔧 完整服务架构

### Backend Server (Go + Gin)
**端口**: 5678 | **容器**: backend-server

#### 核心功能
- **用户管理**: 注册、登录、档案管理、设置配置
- **设备管理**: 设备注册、心跳监控、配置管理、状态追踪
- **猫咪管理**: 猫咪档案、健康记录、统计分析
- **视频记录**: 记录管理、视频服务、AI分析集成
- **家庭组**: 多用户设备共享、权限管理
- **通知系统**: APNs推送、通知设置、消息管理
- **影子模式**: 用户级别配置、后台处理、结果存储

#### 关键特性
- **自动用户配置**: 新用户注册时自动创建影子模式配置
- **异步处理**: 影子模式在后台异步运行，不影响主流程
- **用户级控制**: 每个用户可独立启用/禁用影子模式
- **数据持久化**: 所有配置和结果自动保存到MySQL数据库

### Caby AI (Go + Gin)
**端口**: 8765 | **容器**: caby-ai

#### 核心功能
- **视频分析**: 智能视频内容分析和行为识别
- **猫咪识别**: 基于深度学习的个体识别
- **特征提取**: 高维特征向量生成和存储
- **相似度匹配**: 基于Qdrant的向量相似度搜索
- **批量初始化**: 支持批量猫咪特征向量初始化

#### 影子模式功能
- **特征向量管理**: 初始化、存储、检索猫咪特征向量
- **相似度计算**: 高精度的余弦相似度匹配
- **新猫发现**: 自动识别未知猫咪个体
- **批量处理**: 支持100+猫咪的批量特征初始化

#### API端点
```
POST /api/v1/analyze              # 视频分析
POST /api/v1/admin/init-cats      # 初始化猫咪特征
POST /api/v1/admin/batch-init-cats # 批量初始化猫咪特征
POST /api/v1/admin/test-similarity # 测试相似度匹配
GET  /api/v1/admin/shadow-config  # 获取影子模式配置
```

### Caby Vision (Python + Triton)
**端口**: 8000 | **容器**: caby-vision

#### 核心功能
- **图像识别**: 高精度猫咪个体识别
- **特征向量**: 生成高质量的识别特征向量
- **模型服务**: 基于Triton Inference Server的模型部署
- **批量处理**: 支持批量图像处理和特征提取

#### 模型架构
- **ReID模型**: 猫咪个体重识别模型
- **特征提取**: 512维特征向量生成
- **高精度**: 98%+的识别准确率
- **实时处理**: 毫秒级响应时间

#### API端点
```
POST /v2/models/reid/infer        # 猫咪个体识别
GET  /v2/models/reid/ready        # 模型状态检查
GET  /v2/health/ready             # 健康检查
```

### 数据存储

#### MySQL 8.0
- **用户数据**: 用户信息、设备数据、猫咪档案
- **业务数据**: 视频记录、健康记录、通知消息
- **影子模式**: 用户配置、识别结果、通知记录
- **关系数据**: 家庭组、设备关联、权限管理

#### Qdrant (向量数据库)
- **特征向量**: 猫咪个体特征向量存储
- **相似度搜索**: 高效的向量相似度检索
- **用户隔离**: 基于用户ID的数据隔离
- **高性能**: 毫秒级向量搜索响应

#### MinIO (对象存储)
- **视频文件**: HLS格式的视频片段存储
- **缩略图**: 视频缩略图和关键帧
- **用户文件**: 猫咪头像、用户上传文件
- **备份数据**: 数据备份和归档

### 外部服务

#### Logto (认证服务)
- **OAuth认证**: 标准OAuth 2.0认证流程
- **用户管理**: 用户注册、登录、密码管理
- **令牌管理**: JWT访问令牌和刷新令牌
- **多端支持**: iOS、Android、Web端统一认证

#### Apple Push Notification Service (APNs)
- **推送通知**: iOS设备推送通知
- **证书管理**: APNs证书配置和管理
- **消息队列**: 可靠的消息传递机制
- **批量推送**: 支持批量用户通知

### 服务间通信

#### 数据流向
```
用户请求 → Backend Server → Caby AI → Caby Vision
                ↓              ↓         ↓
            MySQL数据库    Qdrant向量库  ReID模型
                ↓              ↓         ↓
            影子模式处理 ← 特征向量匹配 ← 特征提取
```

#### 认证机制
- **服务间认证**: 基于API密钥的服务间通信
- **用户认证**: 基于JWT的用户身份验证
- **权限控制**: 基于角色的访问控制(RBAC)

### 部署架构

#### Docker Compose部署
```yaml
services:
  backend-server:    # 主业务服务
  caby-ai:          # AI分析服务
  caby-vision:      # 视觉识别服务
  mysql:            # 关系数据库
  qdrant:           # 向量数据库
  minio:            # 对象存储
  nginx:            # 反向代理
```

#### 网络配置
- **内部网络**: 服务间通信网络
- **外部访问**: 通过Nginx反向代理
- **SSL终止**: Nginx处理HTTPS证书
- **负载均衡**: 支持多实例部署

## 🎯 影子模式系统

影子模式是一个基于特征向量的猫咪识别系统，完全集成到后台视频分析流程中，提供更准确的个性化识别结果。

### 🏗️ 系统架构

#### 核心组件
- **Backend Server**: 影子模式完全集成到主业务流程中，支持用户级别配置
- **Caby AI**: 提供猫咪特征向量提取和相似度匹配服务
- **Caby Vision**: 提供高质量的猫咪个体识别特征向量
- **Qdrant**: 向量数据库，存储和检索猫咪特征向量

#### 数据流程
1. **视频分析触发**: 用户上传视频进行分析
2. **用户配置检查**: 系统检查用户是否启用影子模式
3. **异步处理**: 如果启用，在后台异步进行影子模式识别
4. **特征向量匹配**: 使用Qdrant进行高效的向量相似度搜索
5. **结果存储**: 识别结果自动保存到数据库
6. **通知处理**: 根据配置发送相关通知

### 🔧 功能特性

#### 用户级别配置
- **个性化设置**: 每个用户可独立启用/禁用影子模式
- **阈值调整**: 支持自定义相似度阈值和新猫识别阈值
- **通知控制**: 可选择是否接收影子模式相关通知

#### 自动化处理
- **后台集成**: 完全集成到视频分析流程，用户无感知
- **异步执行**: 不影响主要业务流程的性能
- **自动存储**: 结果直接保存到数据库，便于后续分析

#### 高精度识别
- **特征向量**: 基于深度学习的高维特征向量
- **相似度匹配**: 使用余弦相似度进行精确匹配
- **新猫发现**: 自动识别和记录新出现的猫咪

### 📊 数据库结构

#### 新增表结构
- **user_shadow_config**: 用户影子模式配置表
- **shadow_mode_notifications**: 影子模式通知记录表
- **record_analysis**: 扩展了7个影子模式相关字段

#### 影子模式字段
- `shadow_mode_result`: 完整的识别结果JSON
- `shadow_similarity`: 相似度分数
- `shadow_matched_cat_id`: 匹配的猫咪ID
- `shadow_is_new_cat`: 是否为新猫
- `shadow_confidence`: 识别置信度
- `shadow_features_stored`: 特征向量存储状态
- `shadow_model_version`: 模型版本信息

### 🚀 部署和配置

#### 自动用户配置
- **新用户**: 系统自动为新注册用户创建默认影子模式配置（默认禁用）
- **现有用户**: 可通过数据库操作为现有用户启用影子模式

#### 启用影子模式
```sql
-- 为用户启用影子模式
INSERT INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold)
VALUES ('user_123', TRUE, 0.85, 0.70)
ON DUPLICATE KEY UPDATE enabled = TRUE;
```

#### 测试和验证
```bash
# 使用caby_ai测试脚本初始化猫咪特征
cd caby_ai
python scripts/test.py init_shadow_cats

# 测试相似度匹配
python scripts/test.py test_user_similarity

# 测试完整流程
python scripts/test.py test_real_similarity
```

## 🚀 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- MinIO
- Docker & Docker Compose (可选)

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd backend_server
```

2. **安装依赖**
```bash
go mod tidy
```

3. **配置环境**
```bash
cp config/config.yaml.template config/config.yaml
# 编辑 config.yaml 填入实际配置
```

4. **启动依赖服务**
```bash
# 使用 Docker Compose 启动 MySQL 和 MinIO
docker-compose up mysql minio -d
```

5. **运行服务**
```bash
go run main.go
```

服务将在 `http://localhost:5678` 启动。

### Docker 部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 仅启动后端服务
docker-compose up backend-server -d
```

## ⚙️ 配置说明

### 配置文件结构

```yaml
# MySQL 数据库配置
mysql:
  host: "localhost"
  port: 3306
  user: "meowth"
  password: "your_password"
  database: "cats_db"

# MinIO 对象存储配置
minio:
  endpoint: "localhost:9000"
  access_key: "minioadmin"
  secret_key: "minioadmin"
  use_ssl: false

# Logto 认证服务配置
logto:
  endpoint: "https://your-logto-instance.logto.app"
  app_id: "your_app_id"
  app_secret: "your_app_secret"
  api_resource: "https://your-logto-instance.logto.app/api"
  callback_uri: "https://your-domain.com/api/callback"

# Apple Push Notification 配置
aps:
  team_id: "YOUR_TEAM_ID"
  key_id: "YOUR_KEY_ID"
  bundle_id: "com.yourcompany.cabycare"
  auth_key_path: "/path/to/AuthKey.p8"

# AI 分析服务配置
caby_ai:
  url: "http://ai-service:8765"
  auth_token: "your_ai_service_token"
  max_retries: 300
  timeout_minutes: 30

# 设备监控配置
device_config:
  heartbeat_interval: 15    # 设备心跳间隔（分钟）
  status_timeout: 31        # 设备离线超时（分钟）
  statistics_window: 24     # 统计窗口（小时）
```

### 环境变量

创建 `.env` 文件：

```bash
# MySQL
MYSQL_DATABASE=cats_db
MYSQL_USER=meowth
MYSQL_PASSWORD=your_mysql_password
MYSQL_ROOT_PASSWORD=your_root_password

# MinIO
MINIO_ROOT_USER=your_minio_user
MINIO_ROOT_PASSWORD=your_minio_password

# 部署模式
SETUP_MODE=auto
INIT_DB=true
```

## 📚 API 文档

### 基础信息

- **Base URL**: `https://api.caby.care/api`
- **认证方式**: Bearer Token (Logto JWT)
- **数据格式**: JSON

### 认证相关 API

#### 用户认证
```http
GET /api/callback                    # OAuth 回调处理
POST /api/refresh                    # 刷新访问令牌
GET /api/user/info                   # 获取用户信息 [需认证]
```

### 设备管理 API

#### 设备注册与管理
```http
POST /api/devices/register           # 注册新设备
POST /api/devices/heartbeat          # 设备心跳
POST /api/devices/timezone           # 设备时区设置
GET /api/devices/:device_id          # 获取设备信息
GET /api/devices                     # 获取用户设备列表
GET /api/devices/:device_id/status   # 获取设备状态
GET /api/devices/:device_id/statistics # 获取设备统计
GET /api/devices/accessible          # 获取用户可访问的所有设备
```

#### 设备配置
```http
GET /api/devices/:device_id/config   # 获取设备配置
PUT /api/devices/:device_id/config   # 更新设备配置
GET /api/devices/:device_id/setting  # 获取设备设置
PUT /api/devices/:device_id/setting  # 更新设备设置
```

### 猫咪管理 API

#### 猫咪信息管理
```http
POST /api/cats                       # 创建猫咪档案 [需认证]
GET /api/cats/:cat_id               # 获取猫咪信息 [需认证]
PUT /api/cats/:cat_id               # 更新猫咪信息 [需认证]
GET /api/cats                       # 获取用户猫咪列表 [需认证]
```

#### 猫咪健康记录
```http
POST /api/cats/:cat_id/health       # 创建健康记录 [需认证]
GET /api/cats/:cat_id/health        # 获取健康记录列表 [需认证]
GET /api/cats/:cat_id/alerts        # 获取猫咪警报 [需认证]
```

#### 猫咪统计数据
```http
GET /api/cats/:cat_id/metrics/daily     # 获取每日统计 [需认证]
GET /api/cats/:cat_id/metrics/monthly   # 获取月度统计 [需认证]
GET /api/metrics/cats/:cat_id/daily     # 每日指标统计
GET /api/metrics/cats/:cat_id/monthly   # 月度指标统计
```

### 视频记录 API

#### 记录管理
```http
POST /api/records                    # 创建视频记录
GET /api/records                     # 获取记录列表
GET /api/records/device/:device_id   # 获取设备记录
GET /api/records/cat/:cat_id         # 获取猫咪记录
```

#### 视频服务
```http
GET /api/records/videos/list                 # 获取视频列表
GET /api/records/videos/get                  # 获取播放列表
GET /api/records/videos/:folder/:filename    # 获取视频片段
POST /api/records/videos/static/:video_id    # 检查视频是否为静态 [服务间调用]
```

### 用户管理 API

#### 用户信息
```http
GET /api/users/by-username           # 根据用户名获取用户 [需认证]
GET /api/users/:user_id             # 获取用户信息 [需认证]
GET /api/users                      # 获取用户列表 [需认证]
GET /api/users/:user_id/devices     # 获取用户设备 [需认证]
```

#### 用户配置
```http
GET /api/users/:user_id/profile     # 获取用户档案 [需认证]
PUT /api/users/:user_id/profile     # 更新用户档案 [需认证]
GET /api/users/:user_id/settings    # 获取用户设置 [需认证]
PUT /api/users/:user_id/settings    # 更新用户设置 [需认证]
GET /api/users/:user_id/notifications # 获取用户通知 [需认证]
```

### 硬件关联 API

```http
POST /api/devices              # 创建用户硬件关联
GET /api/devices/users/:user_id # 获取用户硬件列表
GET /api/devices/hardware/:hardware_sn # 获取硬件用户
GET /api/devices/check         # 检查关联是否存在
```

### 客户端管理 API

#### 客户端注册与管理
```http
POST /api/clients/register           # 注册新客户端
PUT /api/clients/:client_id          # 更新客户端信息
GET /api/clients/:client_id          # 获取客户端信息
GET /api/clients                     # 获取用户的所有客户端
DELETE /api/clients/:client_id       # 删除客户端
```

#### 推送令牌管理
```http
POST /api/clients/:client_id/token   # 注册/更新推送令牌
DELETE /api/clients/:client_id/token # 删除推送令牌
GET /api/clients/:client_id/token    # 获取推送令牌信息
```

#### 客户端状态
```http
POST /api/clients/:client_id/heartbeat # 客户端心跳
PUT /api/clients/:client_id/status   # 更新客户端状态
```

### 家庭组管理 API

#### 家庭组基本操作
```http
POST /api/family-groups              # 创建家庭组
GET /api/family-groups               # 获取用户的家庭组列表
GET /api/family-groups/:group_id     # 获取家庭组详情
PUT /api/family-groups/:group_id     # 更新家庭组信息
DELETE /api/family-groups/:group_id  # 删除家庭组
```

#### 家庭组成员管理
```http
GET /api/family-groups/:group_id/members        # 获取成员列表
POST /api/family-groups/:group_id/members       # 添加成员
PUT /api/family-groups/:group_id/members/:member_id # 更新成员信息
DELETE /api/family-groups/:group_id/members/:member_id # 移除成员
```

#### 家庭组设备管理
```http
GET /api/family-groups/:group_id/devices        # 获取设备列表
POST /api/family-groups/:group_id/devices       # 添加设备到家庭组
DELETE /api/family-groups/:group_id/devices/:device_id # 从家庭组移除设备
```

#### 家庭组邀请管理
```http
POST /api/family-groups/:group_id/invitations   # 发送邀请
GET /api/family-groups/invitations/received     # 获取收到的邀请
GET /api/family-groups/invitations/sent         # 获取发送的邀请
GET /api/family-groups/invitations/:invitation_id # 获取邀请详情
PUT /api/family-groups/invitations/:invitation_id/process # 处理邀请
DELETE /api/family-groups/invitations/:invitation_id/cancel # 取消邀请
```

### 通知管理 API

```http
GET /api/notifications/settings      # 获取通知设置
PUT /api/notifications/settings      # 更新通知设置
POST /api/notifications              # 创建通知
GET /api/notifications               # 获取用户通知
PUT /api/notifications/:id/read      # 标记为已读
```

### 系统 API

```http
GET /api/health                      # 健康检查
```

## 📋 API 数据格式说明

### 通用响应格式

#### 成功响应
```json
{
  "code": 200,
  "status": "success",
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

### 设备管理 API 数据格式

#### 设备注册请求
```json
{
  "device_id": "CAT_20240222_0001",
  "user_id": "user123",
  "hardware_sn": "HW2024022600001",
  "name": "客厅猫厕所",
  "model": "CabyCare-Pro-V1",
  "firmware_version": "1.0.0",
  "timezone": "Asia/Shanghai"
}
```

#### 设备心跳请求
```json
{
  "device_id": "CAT_20240222_0001",
  "signal_strength": 85,
  "ipv4": "*************",
  "ipv6": "2001:db8::1",
  "storage_usage": 45
}
```

#### 设备配置更新请求
```json
{
  "location": "客厅",
  "room_size": 25.5,
  "install_height": 1.2,
  "install_angle": 45.0,
  "camera_config": "{\"resolution\":\"1080p\",\"fps\":30}",
  "weight_config": "{\"sensitivity\":\"high\"}",
  "ai_config": "{\"detection_threshold\":0.8}"
}
```

#### 设备设置更新请求
```json
{
  "auto_ota_upgrade": "on"  // "on" 或 "off"
}
```

### 猫咪管理 API 数据格式

#### 创建猫咪请求
```json
{
  "name": "小花",
  "birthday": "2023-01-15T00:00:00Z",
  "gender": 1,  // 1-雄性, 2-雌性
  "breed": "英短",
  "color": "银渐层"
}
```

#### 猫咪健康记录请求
```json
{
  "type": "体检",
  "weight": 4.5,
  "temperature": 38.5,
  "description": "定期体检，身体状况良好"
}
```

#### 猫咪统计响应
```json
{
  "cat_id": "cat123",
  "date": "2024-01-15",
  "toilet_count": 3,
  "avg_duration": 120.5,
  "weight": 4.5,
  "health_score": 85.0
}
```

### 视频记录 API 数据格式

#### 创建视频记录请求
```json
{
  "record": {
    "video_id": "video_20240115_001",
    "device_id": "CAT_20240222_0001",
    "start_time": **********,  // Unix 时间戳
    "end_time": **********,
    "weight_litter": 4.5,
    "weight_cat": 4.8,
    "weight_waste": 0.3,
    "type": "shit",
    "status": 1
  },
  "user_id": "user123"
}
```

#### 视频记录响应
```json
{
  "video_id": "video_20240115_001",
  "device_id": "CAT_20240222_0001",
  "start_time": **********,
  "start_time_iso": "2024-01-15T08:00:00Z",
  "end_time": **********,
  "end_time_iso": "2024-01-15T08:02:00Z",
  "weight_litter": 4.5,
  "weight_cat": 4.8,
  "weight_waste": 0.3,
  "type": "shit",
  "status": 1,
  "created_at": "2024-01-15T08:00:00Z"
}
```

### 客户端管理 API 数据格式

#### 客户端注册请求
```json
{
  "user_id": "user123",
  "client_id": "aWFob25lMTU=",  // Base64 编码的设备ID
  "client_type": "ios",  // ios/android/web
  "name": "iPhone 15",
  "model": "iPhone15,2",
  "os_version": "17.2",
  "app_version": "1.0.0"
}
```

#### 客户端令牌注册请求
```json
{
  "client_token": "abc123def456...",
  "token_type": "apns",  // apns/fcm/web
  "is_sandbox": false
}
```

#### 客户端心跳请求
```json
{
  "app_version": "1.0.1",
  "status": 1  // 1-正常, 2-后台, 3-离线
}
```

### 家庭组管理 API 数据格式

#### 创建家庭组请求
```json
{
  "group_name": "我的家庭",
  "description": "家庭成员共享设备"
}
```

#### 添加家庭组成员请求
```json
{
  "user_id": "user456",
  "nickname": "爸爸",
  "role": 1  // 0-普通成员, 1-管理员, 2-拥有者
}
```

#### 添加家庭组设备请求
```json
{
  "device_id": "CAT_20240222_0001"
}
```

#### 创建家庭组邀请请求
```json
{
  "invitee_id": "user789",
  "role": 0,  // 0-普通成员, 1-管理员
  "expire_at": "2024-01-22T00:00:00Z"  // 可选，ISO8601格式
}
```

#### 处理家庭组邀请请求
```json
{
  "action": "accept",  // accept/reject
  "nickname": "妈妈"   // 接受邀请时的昵称，可选
}
```

### 通知管理 API 数据格式

#### 通知设置更新请求
```json
{
  "enable_daily": true,
  "enable_stats": true,
  "quiet_hours_start": 1320,  // 22:00 (分钟数)
  "quiet_hours_end": 480,     // 08:00 (分钟数)
  "timezone": "Asia/Shanghai"
}
```

#### 创建通知请求
```json
{
  "user_id": "user123",
  "type": "daily",
  "title": "猫咪如厕提醒",
  "content": "小花刚刚使用了猫厕所",
  "level": 2,  // 1-低, 2-普通, 3-高
  "extra_data": "{\"cat_id\":\"cat123\",\"device_id\":\"CAT_20240222_0001\"}"
}
```

### 视频服务 API 数据格式

#### 视频列表请求参数
```
GET /api/records/videos/list?path=deviceCAT_20240222_0001&start=2024-01-15&end=2024-01-16
```

- `path`: 设备路径，格式为 `device{device_id}`
- `start`: 开始日期，格式为 `YYYY-MM-DD`
- `end`: 结束日期，格式为 `YYYY-MM-DD`

#### 视频列表响应
```json
[
  {
    "start": **********,
    "duration": "120.5",
    "url": "https://api.caby.care/api/records/videos/get?path=deviceCAT_20240222_0001&start=2024-01-15T08%3A00%3A00%2B08%3A00&duration=120.500",
    "weight_litter": 4.5,
    "weight_cat": 4.8,
    "weight_waste": 0.3
  }
]
```

#### 获取播放列表请求参数
```
GET /api/records/videos/get?path=deviceCAT_20240222_0001&start=2024-01-15T08:00:00+08:00&duration=120.5
```

- `path`: 设备路径
- `start`: 视频开始时间，RFC3339格式
- `duration`: 视频时长（秒），可选

#### 视频片段请求
```
GET /api/records/videos/2024-01-15_08-00-00_hls/segment_001.ts?bucket=deviceCAT_20240222_0001
```

- `bucket`: 存储桶名称，通常为设备路径

#### 检查视频静态状态请求
```json
{
  "user_id": "user123"
}
```

#### 检查视频静态状态响应
```json
{
  "is_static": false,
  "analysis": {
    "video_id": "video_20240115_001",
    "animal_id": "cat123",
    "cat_confidence": 0.95,
    "behavior_type": "normal_poop",
    "is_abnormal": false,
    "abnormal_type": "",
    "abnormal_prob": 0.0,
    "ai_results": "{\"detection_results\":[...]}",
    "created_at": "2024-01-15T08:00:00Z",
    "updated_at": "2024-01-15T08:02:00Z"
  }
}
```

### 用户硬件关联 API 数据格式

#### 创建用户硬件关联请求
```json
{
  "user_id": "user123",
  "hardware_sn": "HW2024022600001",
  "remark": "客厅设备"
}
```

#### 检查用户硬件关联请求参数
```
GET /api/devices/check?user_id=user123&hardware_sn=HW2024022600001
```

#### 用户硬件关联响应
```json
{
  "id": 1,
  "user_id": "user123",
  "hardware_sn": "HW2024022600001",
  "status": 1,
  "remark": "客厅设备",
  "created_at": "2024-01-15T08:00:00Z",
  "updated_at": "2024-01-15T08:00:00Z",
  "user": {
    "user_id": "user123",
    "username": "张三",
    "email": "<EMAIL>"
  }
}
```

### 用户管理 API 数据格式

#### 用户登录请求
```json
{
  "username": "zhangsan",
  "password": "password123"
}
```

#### 用户登录响应
```json
{
  "user_id": "user123",
  "username": "zhangsan",
  "email": "<EMAIL>",
  "display_name": "张三",
  "avatar_url": "https://example.com/avatar.jpg",
  "status": 1,
  "created_at": "2024-01-15T08:00:00Z",
  "updated_at": "2024-01-15T08:00:00Z"
}
```

#### 用户档案更新请求
```json
{
  "display_name": "张三",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "phone": "+86-13800138000",
  "address": "北京市朝阳区"
}
```

#### 用户设置更新请求
```json
{
  "language": "zh-CN",
  "timezone": "Asia/Shanghai",
  "theme": "light",
  "notifications_enabled": true
}
```

#### 用户通知列表响应
```json
[
  {
    "id": 1,
    "user_id": "user123",
    "type": "daily",
    "title": "猫咪如厕提醒",
    "content": "小花刚刚使用了猫厕所",
    "level": 2,
    "is_read": false,
    "read_time": null,
    "extra_data": "{\"cat_id\":\"cat123\",\"device_id\":\"CAT_20240222_0001\"}",
    "created_at": "2024-01-15T08:00:00Z"
  }
]
```

### 认证相关 API 数据格式

#### 刷新令牌请求
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 刷新令牌响应
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 3600,
  "token_type": "Bearer"
}
```

#### 用户信息响应
```json
{
  "user_id": "user123",
  "username": "zhangsan",
  "email": "<EMAIL>",
  "display_name": "张三",
  "avatar_url": "https://example.com/avatar.jpg",
  "phone": "+86-13800138000",
  "address": "北京市朝阳区",
  "language": "zh-CN",
  "timezone": "Asia/Shanghai",
  "status": 1,
  "created_at": "2024-01-15T08:00:00Z",
  "updated_at": "2024-01-15T08:00:00Z"
}
```

### AI 分析服务 API 数据格式

#### AI 分析请求 (发送到 Caby AI)
```json
{
  "video_id": "video_20240115_001",
  "device_id": "CAT_20240222_0001",
  "user_id": "user123",
  "start_time": "2024-01-15T08:00:00Z",
  "end_time": "2024-01-15T08:02:00Z",
  "video_path": "https://api.caby.care/api/records/videos/get?device_id=CAT_20240222_0001&start=2024-01-15T08:00:00Z",
  "known_cat_ids": ["f3ce1b02b2c1d755421000", "f3ce1b02b40e9477c21000"]
}
```

#### AI 分析响应 (来自 Caby AI)
```json
{
  "video_id": "video_20240115_001",
  "animal_id": "f3ce1b02b2c1d755421000",
  "cat_confidence": 0.8756,
  "behavior_type": "normal_poop",
  "is_abnormal": false,
  "abnormal_type": "",
  "abnormal_prob": 0.05,
  "ai_results": "{\"keyframes\":15,\"predicted_cat\":\"小黑\",\"confidence\":0.8756,\"class_probabilities\":{\"小黑\":0.8756,\"小白\":0.1123,\"小花\":0.0121}}",
  "created_at": "2024-01-15T08:00:00Z",
  "updated_at": "2024-01-15T08:02:00Z"
}
```

#### 猫咪名称到ID映射 (当前版本)
```
小黑 -> f3ce1b02b2c1d755421000
小白 -> f3ce1b02b40e9477c21000
小花 -> f3ce1b02b40ed223821000
```

#### AI 分析工作流程
1. Backend Server 接收视频记录请求
2. 调用 Caby AI 的 `/api/v1/analyze` 端点
3. Caby AI 检测视频是否为静态内容
4. 如果非静态，从 Backend Server 获取缩略图
5. 将缩略图发送到 Caby Vision 进行猫咪识别
6. 映射猫咪名称到ID并返回分析结果
7. Backend Server 存储分析结果

### 时间格式说明

- **Unix 时间戳**: 用于数据库存储和内部处理，如 `**********`
- **ISO8601 格式**: 用于API响应和用户界面显示，如 `"2024-01-15T08:00:00Z"`
- **RFC3339 格式**: 用于查询参数，如 `"2024-01-15T08:00:00+08:00"`

### 状态码说明

#### 设备状态
- `1`: 正常
- `2`: 离线
- `3`: 故障

#### 猫咪性别
- `1`: 雄性
- `2`: 雌性

#### 家庭组角色
- `0`: 普通成员
- `1`: 管理员
- `2`: 拥有者

#### 邀请状态
- `0`: 待处理
- `1`: 已接受
- `2`: 已拒绝
- `3`: 已过期

#### 通知级别
- `1`: 低优先级
- `2`: 普通优先级
- `3`: 高优先级

### 分页参数

对于支持分页的API，可以使用以下查询参数：

```
?page=1&page_size=20&sort=created_at&order=desc
```

- `page`: 页码，从1开始
- `page_size`: 每页数量，默认20，最大100
- `sort`: 排序字段
- `order`: 排序方向，`asc` 或 `desc`

#### 分页响应格式
```json
{
  "code": 200,
  "status": "success",
  "message": "获取成功",
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

### 错误处理说明

#### HTTP 状态码

- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未授权，需要登录
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `409 Conflict`: 资源冲突（如重复创建）
- `422 Unprocessable Entity`: 请求格式正确但语义错误
- `429 Too Many Requests`: 请求频率超限
- `500 Internal Server Error`: 服务器内部错误
- `502 Bad Gateway`: 网关错误
- `503 Service Unavailable`: 服务不可用

#### 常见错误响应示例

##### 参数验证错误
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "device_id is required"
}
```

##### 认证错误
```json
{
  "code": 401,
  "message": "未授权访问",
  "error": "Invalid or expired token"
}
```

##### 权限错误
```json
{
  "code": 403,
  "message": "权限不足",
  "error": "You don't have permission to access this resource"
}
```

##### 资源不存在
```json
{
  "code": 404,
  "message": "资源不存在",
  "error": "Device not found"
}
```

##### 业务逻辑错误
```json
{
  "code": 422,
  "message": "业务逻辑错误",
  "error": "Cannot delete device with active recordings"
}
```

##### 服务器错误
```json
{
  "code": 500,
  "message": "服务器内部错误",
  "error": "Database connection failed"
}
```

### 请求头说明

#### 必需的请求头

- `Content-Type: application/json`: 对于POST/PUT请求
- `Authorization: Bearer <token>`: 对于需要认证的API

#### 可选的请求头

- `Accept-Language: zh-CN,en-US`: 指定响应语言
- `X-Request-ID: uuid`: 请求追踪ID
- `User-Agent: CabyCare-iOS/1.0.0`: 客户端标识

### 响应头说明

#### 常见响应头

- `Content-Type: application/json`: 响应内容类型
- `X-Request-ID: uuid`: 请求追踪ID（回显）
- `X-Rate-Limit-Remaining: 100`: 剩余请求次数
- `X-Rate-Limit-Reset: 1640995200`: 限制重置时间

### 特殊字段说明

#### 设备ID格式
- 格式: `CAT_YYYYMMDD_NNNN`
- 示例: `CAT_20240222_0001`

#### 硬件序列号格式
- 格式: `HWYYYYYMMDDNNNNN`
- 示例: `HW2024022600001`

#### 视频ID格式
- 格式: 24位十六进制字符串
- 示例: `v160287367175999488`

#### 用户ID格式
- 格式: 16位十六进制字符串
- 示例: `0220280ee0021000`

## 🔐 认证与授权

### 认证流程

1. **用户登录**: 通过 Logto 进行 OAuth 认证
2. **获取令牌**: 认证成功后获取 JWT 访问令牌
3. **API 调用**: 在请求头中携带 `Authorization: Bearer <token>`
4. **令牌刷新**: 使用刷新令牌更新访问令牌

### 权限控制

- **公开接口**: 无需认证即可访问
- **用户接口**: 需要有效的用户令牌
- **服务接口**: 需要服务间通信令牌

## 📊 数据模型

### 核心实体

- **User**: 用户信息
- **Device**: 智能猫厕所设备
- **Cat**: 猫咪档案
- **RecordShit**: 如厕记录
- **CatHealth**: 猫咪健康记录
- **FamilyGroup**: 家庭组
- **Client**: 客户端设备
- **Notification**: 通知消息

### 关系说明

- 用户可以拥有多个设备和猫咪
- 设备可以记录多条如厕记录
- 猫咪可以有多条健康记录
- 家庭组可以包含多个成员和设备
- 用户可以有多个客户端设备

## 🔧 开发指南

### 添加新 API

1. 在 `pkg/` 下相应模块添加业务逻辑
2. 在 `api/` 下添加路由处理
3. 更新 Swagger 注释
4. 运行 `swag init` 生成文档

### 数据库迁移

```bash
# 执行数据库初始化脚本
mysql -u root -p < database/mysql.sql
```

### 代码规范

- 使用 Go 标准格式化工具
- 添加适当的错误处理
- 编写单元测试
- 添加 Swagger 文档注释

## 🧪 测试

### 使用测试脚本

#### Backend Server 测试
Backend Server 提供了统一的测试脚本，用于测试各种API功能：

```bash
# 运行所有测试
python scripts/test.py

# 运行特定测试
python scripts/test.py health analyze thumbnail

# 批量测试分析功能 (测试多条记录)
python scripts/test.py batch --batch-count 50

# 使用自定义参数
python scripts/test.py --url http://localhost:5678 --api-key your-api-key

# 详细输出
python scripts/test.py --verbose
```

#### Caby AI 影子模式测试
Caby AI 提供了专门的影子模式测试功能：

```bash
cd caby_ai

# 初始化猫咪特征向量（使用配置文件中的100只猫）
python scripts/test.py init_shadow_cats

# 测试用户相似度匹配
python scripts/test.py test_user_similarity

# 测试真实相似度匹配
python scripts/test.py test_real_similarity

# 清理测试数据
python scripts/test.py cleanup_test_data

# 运行所有影子模式测试
python scripts/test.py init_shadow_cats test_user_similarity test_real_similarity

# 输出示例:
# 🚀 初始化影子模式猫咪特征 (用户: 022b605dc3421000)...
# 📝 从配置文件加载了 3 只猫的配置
# 1. 处理 小花 (ID: f3ce1b02b5f1a8b9d31000) - 100 张图片
# ✅ 小花 成功处理 100/100 张图片
# 📊 总计: 3 只猫, 300 张图片
# ✅ 猫咪特征初始化完成!
```

#### Caby Vision 测试
Caby Vision 提供了模型和识别测试：

```bash
cd caby_vision

# 测试模型状态
python scripts/test.py model_status

# 测试猫咪识别
python scripts/test.py cat_recognition

# 批量测试
python scripts/test.py batch_test --count 100
```

### 批量记录重新筛查

使用测试脚本对历史记录进行重新分析：

```bash
# 批量测试1000条记录
python scripts/test.py batch --batch-count 1000

# 这将创建测试记录并触发AI分析，用于验证系统性能
```

### 手动API测试

```bash
# 健康检查
curl -H "Authorization: Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga" \
  http://localhost:5678/api/health

# 触发视频分析
curl -X POST \
  -H "Authorization: Bearer 03U66tGbSQtHGrh9IyBDjRYaSeQukFga" \
  -H "Content-Type: application/json" \
  -d '{
    "video_id": "test_video_001",
    "device_id": "CAT_20240222_0001",
    "user_id": "022b605dc3421000",
    "start_time": "2025-01-01T10:00:00Z",
    "end_time": "2025-01-01T10:05:00Z",
    "video_path": "https://example.com/videos/test.m3u8",
    "known_cat_ids": ["f3ce1b02b2c1d755421000"]
  }' \
  http://localhost:5678/api/analyze
```

## 🚀 部署

### 生产环境部署

1. **构建镜像**
```bash
docker build -f Dockerfile.backend -t cabycare-backend .
```

2. **配置环境变量**
```bash
# 设置生产环境配置
export CONFIG_PATH=/app/config/config.yaml
```

3. **启动服务**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### 监控与日志

- 应用日志输出到标准输出
- 使用 Nginx 作为反向代理
- 配置 SSL 证书
- 设置健康检查

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接配置是否正确

2. **MinIO 连接失败**
   - 确认 MinIO 服务正常运行
   - 检查访问密钥配置

3. **认证失败**
   - 验证 Logto 配置
   - 检查令牌是否过期

4. **推送通知失败**
   - 确认 APNs 证书配置
   - 检查设备令牌有效性

### 日志查看

```bash
# 查看应用日志
docker-compose logs backend-server

# 查看数据库日志
docker-compose logs mysql

# 查看 MinIO 日志
docker-compose logs minio
```

## 📈 性能优化

- 使用数据库连接池
- 实现 Redis 缓存
- 优化数据库查询
- 使用 CDN 加速静态资源
- 向量数据库索引优化
- 异步处理队列

## 🎯 影子模式管理

### 用户配置管理

#### 启用用户影子模式
```sql
-- 为特定用户启用影子模式
INSERT INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold, top_k, notification_enabled)
VALUES ('022b605dc3421000', TRUE, 0.85, 0.70, 5, FALSE)
ON DUPLICATE KEY UPDATE enabled = TRUE;
```

#### 批量启用影子模式
```sql
-- 为所有活跃用户启用影子模式
INSERT INTO user_shadow_config (user_id, enabled, similarity_threshold, new_cat_threshold, top_k, notification_enabled)
SELECT user_id, TRUE, 0.85, 0.70, 5, FALSE
FROM users
WHERE status = 1
ON DUPLICATE KEY UPDATE enabled = TRUE;
```

#### 查看影子模式统计
```sql
-- 查看影子模式使用统计
SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN enabled = 1 THEN 1 END) as enabled_users,
    AVG(similarity_threshold) as avg_similarity_threshold,
    AVG(new_cat_threshold) as avg_new_cat_threshold
FROM user_shadow_config;

-- 查看影子模式识别结果统计
SELECT
    COUNT(*) as total_records,
    COUNT(shadow_mode_result) as shadow_processed,
    COUNT(CASE WHEN shadow_is_new_cat = 1 THEN 1 END) as new_cats_found,
    AVG(shadow_similarity) as avg_similarity,
    AVG(shadow_confidence) as avg_confidence
FROM record_analysis
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY);
```

### 数据监控

#### 影子模式性能监控
```sql
-- 创建影子模式监控视图
CREATE VIEW shadow_mode_stats AS
SELECT
    DATE(ra.created_at) as date,
    COUNT(*) as total_analyses,
    COUNT(ra.shadow_mode_result) as shadow_processed,
    COUNT(CASE WHEN ra.shadow_is_new_cat = 1 THEN 1 END) as new_cats_discovered,
    AVG(ra.shadow_similarity) as avg_similarity,
    AVG(ra.shadow_confidence) as avg_confidence,
    COUNT(DISTINCT d.user_id) as active_users
FROM record_analysis ra
JOIN record_shit rs ON ra.video_id = rs.video_id
JOIN devices d ON rs.device_id = d.device_id
JOIN user_shadow_config usc ON d.user_id = usc.user_id AND usc.enabled = 1
WHERE ra.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(ra.created_at)
ORDER BY date DESC;
```

#### 特征向量存储监控
```bash
# 检查Qdrant中的向量数量
curl -X GET "http://localhost:6333/collections/cat_features/info"

# 查看用户向量分布
curl -X POST "http://localhost:6333/collections/cat_features/points/search" \
  -H "Content-Type: application/json" \
  -d '{
    "vector": [0.1, 0.2, ...],
    "limit": 10,
    "filter": {
      "must": [
        {"key": "user_id", "match": {"value": "022b605dc3421000"}}
      ]
    }
  }'
```

### 故障排除

#### 常见问题

1. **影子模式不工作**
   ```sql
   -- 检查用户配置
   SELECT * FROM user_shadow_config WHERE user_id = 'your_user_id';

   -- 检查全局影子模式状态
   -- 查看caby_ai日志确认服务状态
   ```

2. **特征向量匹配失败**
   ```bash
   # 检查Qdrant连接
   curl -X GET "http://localhost:6333/health"

   # 检查向量集合状态
   curl -X GET "http://localhost:6333/collections"
   ```

3. **识别准确率低**
   ```sql
   -- 分析识别结果分布
   SELECT
     shadow_confidence,
     COUNT(*) as count
   FROM record_analysis
   WHERE shadow_mode_result IS NOT NULL
   GROUP BY ROUND(shadow_confidence, 1)
   ORDER BY shadow_confidence DESC;
   ```

#### 日志监控
```bash
# Backend Server影子模式日志
docker-compose logs backend-server | grep -i shadow

# Caby AI影子模式日志
docker-compose logs caby-ai | grep -i shadow

# Qdrant向量数据库日志
docker-compose logs qdrant
```

### 数据备份和恢复

#### 向量数据备份
```bash
# 备份Qdrant数据
docker exec qdrant-container qdrant-backup --collection cat_features --output /backup/

# 恢复Qdrant数据
docker exec qdrant-container qdrant-restore --collection cat_features --input /backup/
```

#### 配置数据备份
```bash
# 备份影子模式配置
mysqldump -u meowth -p cats_db user_shadow_config shadow_mode_notifications > shadow_mode_backup.sql

# 恢复配置数据
mysql -u meowth -p cats_db < shadow_mode_backup.sql
```

---

**CabyCare** - 让智能猫厕所更懂你的猫咪 🐱

### 🎯 影子模式特性总结

- **🔧 用户级配置**: 每个用户可独立控制影子模式
- **⚡ 后台集成**: 完全集成到视频分析流程，用户无感知
- **🎯 高精度识别**: 基于深度学习特征向量，98%+准确率
- **🔄 自动处理**: 异步处理，不影响主业务性能
- **💾 数据持久化**: 配置和结果自动保存到数据库
- **📊 实时监控**: 完整的监控和统计功能
- **🚀 可扩展**: 支持大规模用户和猫咪数据
