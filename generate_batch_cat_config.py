#!/usr/bin/env python3
"""
为用户0220280ee0021000生成每只猫100张图片的批量初始化配置
"""

import json
import os
import random
from collections import defaultdict

def load_annotations(annotations_path):
    """加载annotations.json文件"""
    with open(annotations_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def analyze_cats(annotations):
    """分析猫咪数据"""
    cat_images = defaultdict(list)
    
    for image_name, data in annotations.items():
        category = data.get('category', '')
        if category in ['小黑', '小白', '小花']:
            cat_images[category].append(image_name)
    
    return cat_images

def select_images_for_batch_init(cat_images, images_per_cat=100):
    """为每只猫选择用于批量初始化的图片"""
    selected = {}
    
    for cat_name, images in cat_images.items():
        if len(images) >= images_per_cat:
            # 随机选择指定数量的图片
            selected[cat_name] = random.sample(images, images_per_cat)
        else:
            # 如果图片不够，使用所有可用图片
            selected[cat_name] = images
            print(f"⚠️  {cat_name} 只有 {len(images)} 张图片，少于要求的 {images_per_cat} 张")
    
    return selected

def create_batch_init_config(selected_images, image_dir, user_id="0220280ee0021000"):
    """创建批量初始化配置"""
    
    # 固定的cat_id
    cat_ids = {
        '小黑': "f3ce1b02b2c1d755421000",
        '小白': "f3ce1b02b40e9477c21000", 
        '小花': "f3ce1b02b5f1a8b9d31000"
    }
    
    config = {
        "user_id": user_id,
        "cats": []
    }
    
    for cat_name, images in selected_images.items():
        cat_id = cat_ids[cat_name]
        
        # 为每只猫创建一个条目，包含所有选中的图片
        cat_config = {
            "cat_id": cat_id,
            "name": cat_name,
            "images": []
        }
        
        for image in images:
            image_path = os.path.join(image_dir, image)
            cat_config["images"].append(image_path)
        
        config["cats"].append(cat_config)
    
    return config

def main():
    # 配置路径
    annotations_path = "/home/<USER>/animsi/caby_training/tagging/annotations.json"
    image_dir = "/home/<USER>/animsi/caby_training/dataset/renamed_thumbnails"
    output_file = "cats_batch_init_config.json"
    user_id = "0220280ee0021000"
    
    print("🐱 生成批量猫咪初始化配置文件")
    print("=" * 60)
    print(f"用户ID: {user_id}")
    print(f"每只猫图片数量: 100")
    
    # 检查文件是否存在
    if not os.path.exists(annotations_path):
        print(f"❌ annotations.json文件不存在: {annotations_path}")
        return 1
    
    if not os.path.exists(image_dir):
        print(f"❌ 图片目录不存在: {image_dir}")
        return 1
    
    # 加载annotations
    print("\n📄 加载annotations.json...")
    annotations = load_annotations(annotations_path)
    print(f"   总共 {len(annotations)} 个标注")
    
    # 分析猫咪数据
    print("\n🔍 分析猫咪数据...")
    cat_images = analyze_cats(annotations)
    
    for cat_name, images in cat_images.items():
        print(f"   {cat_name}: {len(images)} 张图片")
    
    if not cat_images:
        print("❌ 没有找到有效的猫咪数据")
        return 1
    
    # 选择图片
    print("\n🎯 选择图片进行批量初始化...")
    selected_images = select_images_for_batch_init(cat_images, images_per_cat=100)
    
    for cat_name, images in selected_images.items():
        print(f"   {cat_name}: 选择了 {len(images)} 张图片")
    
    # 验证图片文件存在
    print("\n✅ 验证图片文件...")
    missing_files = []
    total_images = 0
    
    for cat_name, images in selected_images.items():
        for img in images:
            img_path = os.path.join(image_dir, img)
            if not os.path.exists(img_path):
                missing_files.append(img_path)
            total_images += 1
    
    if missing_files:
        print("❌ 以下图片文件不存在:")
        for file in missing_files[:10]:  # 只显示前10个
            print(f"   - {file}")
        if len(missing_files) > 10:
            print(f"   ... 还有 {len(missing_files) - 10} 个文件不存在")
        return 1
    
    print(f"   所有 {total_images} 张图片文件都存在 ✅")
    
    # 创建配置
    print(f"\n📝 生成批量配置文件...")
    config = create_batch_init_config(selected_images, image_dir, user_id)
    
    # 保存配置文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 配置文件已保存: {output_file}")
    
    # 显示配置摘要
    print(f"\n📋 配置摘要:")
    print(f"   用户ID: {config['user_id']}")
    for cat in config['cats']:
        print(f"   🐱 {cat['name']} (ID: {cat['cat_id']})")
        print(f"      图片数量: {len(cat['images'])} 张")
    
    print(f"\n🚀 使用方法:")
    print(f"   需要创建专门的批量初始化脚本来处理这个配置文件")
    
    return 0

if __name__ == "__main__":
    exit(main())
